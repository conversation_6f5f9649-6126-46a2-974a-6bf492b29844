import "react-native-gesture-handler";
import React from "react";
import Routes from "./app/Navigations/Route";
import { Provider } from "react-redux";
import store from "./app/redux/store";
import { useFonts } from "expo-font";
import { ApolloProvider } from "@apollo/client";
import client from "./app/api/appoloClient";
import { Text, View } from "react-native";

const App = () => {
  useFonts({
    JostRegular: require("./app/assets/fonts/Jost-Regular.ttf"),
    JostSemiBold: require("./app/assets/fonts/Jost-SemiBold.ttf"),
    JostBold: require("./app/assets/fonts/Jost-Bold.ttf"),
    JostMedium: require("./app/assets/fonts/Jost-Medium.ttf"),
    JostLight: require("./app/assets/fonts/Jost-Light.ttf"),
    PoppinsBlack: require("./app/assets/fonts/Poppins-Black.ttf"),
    PoppinsBlackItalic: require("./app/assets/fonts/Poppins-BlackItalic.ttf"),
    PoppinsBold: require("./app/assets/fonts/Poppins-Bold.ttf"),
    PoppinsBoldItalic: require("./app/assets/fonts/Poppins-BoldItalic.ttf"),
    PopppinsExtraBold: require("./app/assets/fonts/Poppins-ExtraBold.ttf"),
    PopppinsExtraBoldItalic: require("./app/assets/fonts/Poppins-ExtraBoldItalic.ttf"),
    PoppinsExtraLight: require("./app/assets/fonts/Poppins-ExtraLight.ttf"),
    PoppinsExtraLightItalic: require("./app/assets/fonts/Poppins-ExtraLightItalic.ttf"),
    PoppinsItalic: require("./app/assets/fonts/Poppins-Italic.ttf"),
    PoppinsLight: require("./app/assets/fonts/Poppins-Light.ttf"),
    PoppinsLightItalic: require("./app/assets/fonts/Poppins-LightItalic.ttf"),
    PoppinsMedium: require("./app/assets/fonts/Poppins-Medium.ttf"),
    PoppinsMediumItalic: require("./app/assets/fonts/Poppins-MediumItalic.ttf"),
    PoppinsRegular: require("./app/assets/fonts/Poppins-Regular.ttf"),
    PoppinsSemiBold: require("./app/assets/fonts/Poppins-SemiBold.ttf"),
    PoppinsSemiBoldItalic: require("./app/assets/fonts/Poppins-SemiBoldItalic.ttf"),
    PoppinsThin: require("./app/assets/fonts/Poppins-Thin.ttf"),
    PoppinsThinItalic: require("./app/assets/fonts/Poppins-ThinItalic.ttf"),
    DMSansSemiBold: require("./app/assets/fonts/DMSans-SemiBold.ttf"),
    DMSansMedium: require("./app/assets/fonts/DMSans-Medium.ttf"),
    DMSansRegular: require("./app/assets/fonts/DMSans-Regular.ttf"),
    DMSansBlack: require("./app/assets/fonts/DMSans-Black.ttf"),
    DMSansBold: require("./app/assets/fonts/DMSans-Bold.ttf"),
    DMSansExtraBold: require("./app/assets/fonts/DMSans-ExtraBold.ttf"),
    DMSansLight: require("./app/assets/fonts/DMSans-Light.ttf"),
    WorkSansMedium: require("./app/assets/fonts/WorkSans-Medium.ttf"),
    SoraMedium: require("./app/assets/fonts/Sora-Medium.ttf"),
    SoraRegular: require("./app/assets/fonts/Sora-Regular.ttf"),
  });

  return (
    <ApolloProvider client={client}>
      <Provider store={store}>
        <Routes />
      </Provider>
    </ApolloProvider>
  );
};

export default App;
