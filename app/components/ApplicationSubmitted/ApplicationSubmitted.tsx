import { View, Text, StyleSheet } from "react-native";
import React, { useEffect, useState } from "react";
import { DMSansFONTS, FONTS, FONTWEIGHT } from "@/app/constants/theme";
import { ActivityIndicator } from "react-native-paper";

export default function ApplicationSubmitted({ navigation }: any) {
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    setTimeout(() => {
      navigation.navigate("DrawerNavigation", { screen: "Home" });
    }, 1500);
  });
  return (
    <View style={styles.container}>
      <>
        <View>
          <Text style={styles.title}>Application Submitted</Text>
        </View>
        <View style={{ width: "90%" }}>
          <Text style={styles.description}>
            To comply with the e-Invoice implementation by the Malaysia's
            IRBM/LHDN, we seek your help to input the mandatory info that was
            required under the e-Invoice implementation.
          </Text>
        </View>
      </>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 11,
  },
  title: {
    fontFamily: "JostBold",
    color: "#343434",
    fontSize: 22,
  },
  description: {
    textAlign: "center",
    ...DMSansFONTS.fontRegular,
    color: "#808080",
    fontSize: 12,
  },
});
