import React, { memo, useCallback, useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableWithoutFeedback,
  Image,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import {
  ButtonLabel,
  COLORS,
  FONTS,
  FONTWEIGHT,
  SIZES,
} from "../../constants/theme";
import Button from "../Button/Button";
import Header from "../../layout/Header";
import { useMutation } from "@apollo/client";
import { ADD_LINES_TO_CART, CREATE_CART } from "../../api/cartQuery";
import { useNavigation } from "@react-navigation/native";
import { ActivityIndicator } from "react-native-paper";
import AddIcon from "../../assets/icons/addicon.png";
import RemoveIcon from "../../assets/icons/removeIcon.png";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  runOnJS,
} from "react-native-reanimated";
import { useDispatch, useSelector } from "react-redux";
import { setCartId } from "../../redux/reducer/cartReducer";
interface ButtonProps {
  modalVisible?: any;
  setModalVisible?: any;
  currentProductDetails?: any;
  buttonWidth?: any;
  children?: any;
  height?: any;
  buttonTitle?: any;
  leftbuttonTitle?: any;
  rightButtonTitle?: any;
  leftIcon?: any;
  rightIcon?: any;
  clearAllBtn?: any;
  headerEnabled?: any;
  noButtton?: any;
  planBottomSheet?: any;
  navbarTitle?: any;
  isCloseButtonRequired?: any;
  isBackBtnRequired?: any;
  modelTitle?: any;
  onPressRightBtn?: any;
  onPressLeftBtn?: any;
  leftIconWidth?: any;
  modelTitleTextComponent?: any;
  onQuantityDecrease?: () => void;
  onQuantityIncrease?: () => void;
}
const BottomNavModal: React.FC<ButtonProps> = ({
  modalVisible,
  setModalVisible,
  currentProductDetails,
  buttonWidth,
  children,
  height,
  leftbuttonTitle = false,
  rightButtonTitle = false,
  leftIcon = false,
  rightIcon = false,
  clearAllBtn = false,
  headerEnabled = false,
  noButtton = false,
  buttonTitle,
  planBottomSheet = false,
  navbarTitle,
  isCloseButtonRequired = true,
  isBackBtnRequired = true,
  modelTitle = "",
  onPressRightBtn = () => {},
  onPressLeftBtn = () => {},
  leftIconWidth = false,
  modelTitleTextComponent = null,
  onQuantityDecrease = () => {},
  onQuantityIncrease = () => {},
}: any) => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { cartId } = useSelector((state: any) => state.cart);

  const closeBottomSheet = useCallback(() => {
    setModalVisible(false);
  }, [modalVisible]);
  const [quantities, setQuantities] = useState<{ [variantId: string]: number }>(
    {}
  );
  const [createCart, { loading, error }] = useMutation(CREATE_CART);
  const [addCart, { loading: addLoading }] = useMutation(ADD_LINES_TO_CART);
  const modalAnimY = useSharedValue(1000); // Initialize the shared value for Y translation
  const [isRendered, setIsRendered] = useState(modalVisible);

  useEffect(() => {
    if (modalVisible) {
      setIsRendered(true); // Ensure the modal is rendered before animating
      modalAnimY.value = withTiming(0, {
        duration: 500, // Slow down the animation by increasing the duration
        easing: Easing.out(Easing.quad), // Use a more gentle easing for a smoother effect
      });
    } else {
      modalAnimY.value = withTiming(
        1000,
        {
          duration: 300,
          easing: Easing.in(Easing.ease), // Smooth easing for closing animation
        },
        (finished) => {
          if (finished) {
            runOnJS(setIsRendered)(false); // Hide the modal after animation
          }
        }
      );
    }
  }, [modalVisible]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: modalAnimY.value }],
    };
  });

  useEffect(() => {
    if (modalVisible) {
      setQuantities({});
    }
  }, [modalVisible]);

  const handleIncrease = (variantId: string) => {
    setQuantities((prev) => ({
      ...prev,
      [variantId]: (prev[variantId] || 1) + 1,
    }));
  };

  const handleDecrease = (variantId: string) => {
    setQuantities((prev) => ({
      ...prev,
      [variantId]: Math.max(1, (prev[variantId] || 1) - 1),
    }));
  };

  const handleAddToBag = () => {
    if (loading || addLoading) return;
    if (!currentProductDetails || currentProductDetails.length === 0) {
      alert("No product selected.");
      return;
    }

    const cartLines = currentProductDetails.map((variant: any) => ({
      merchandiseId: variant.id,
      quantity: quantities[variant.id] || 1,
    }));

    // console.log("CartLines: ", cartLines);

    if (cartId) {
      addCart({
        variables: {
          cartId,
          lines: cartLines,
        },
      })
        .then((response: any) => {
          if (response.data.cartLinesAdd.userErrors.length > 0) {
            alert("Error: " + response.data.cartLinesAdd.userErrors[0].message);
          } else {
            Alert.alert("Success", "Added to cart successfully!", [
              {
                text: "OK",
                onPress: () => {
                  const cartId = response.data.cartLinesAdd.cart.id;
                  // console.log("Cart Id: ", cartId);
                  dispatch(setCartId(cartId));
                  navigation.push("MyCart", {
                    cartId,
                    cartLines,
                  });
                  closeBottomSheet();
                },
              },
            ]);
            // closeBottomSheet()
          }
        })
        .catch((err) => {
          alert("Something went wrong. Please try again.");
          console.error("Cart addition error:", err);
        });
    } else {
      createCart({
        variables: {
          lines: cartLines,
        },
      })
        .then((response: any) => {
          if (response.data.cartCreate.userErrors.length > 0) {
            alert("Error: " + response.data.cartCreate.userErrors[0].message);
          } else {
            Alert.alert("Success", "Added to cart successfully!", [
              {
                text: "OK",
                onPress: () => {
                  const cartId = response.data.cartCreate.cart.id;
                  // console.log("Cart Id: ", cartId);
                  dispatch(setCartId(cartId));
                  navigation.push("MyCart", {
                    cartId,
                    cartLines,
                  });
                  closeBottomSheet();
                },
              },
            ]);
            // closeBottomSheet()
          }
        })
        .catch((err) => {
          alert("Something went wrong. Please try again.");
          console.error("Cart creation error:", err);
        });
    }
  };

  return (
    <SafeAreaProvider>
      {isRendered && (
        <Modal
          animationType="none"
          transparent={true}
          visible={true}
          onRequestClose={() => setModalVisible(false)}
        >
          <TouchableWithoutFeedback onPress={() => closeBottomSheet()}>
            <View style={styles.modalOverlay}>
              <TouchableWithoutFeedback>
                <Animated.View
                  style={[
                    animatedStyle,
                    styles.bottomNavContainer,
                    {
                      maxHeight: height ? height : "80%",
                      height: height,
                      borderTopLeftRadius: noButtton ? 20 : height ? 0 : 20,
                      borderTopRightRadius: noButtton ? 20 : height ? 0 : 20,
                      backgroundColor: COLORS.background,
                      // paddingRight: 5,
                      padding: planBottomSheet ? 0 : 20,
                      // gap: planBottomSheet?10:0,
                    },
                  ]}
                >
                  {headerEnabled === false
                    ? planBottomSheet && (
                        <View style={{ marginTop: 20, marginHorizontal: 12 }}>
                          <Header
                            title={navbarTitle}
                            leftIcon={isBackBtnRequired ? "back" : null}
                            rightIcon={isCloseButtonRequired && "close"}
                            rightIconhandler={closeBottomSheet}
                          />
                        </View>
                      )
                    : null}
                  {headerEnabled && !planBottomSheet && (
                    <Header
                      title={navbarTitle || ""}
                      leftIcon="back"
                      rightIcon={isCloseButtonRequired && "close"}
                      rightIconhandler={closeBottomSheet}
                    />
                  )}
                  {modelTitleTextComponent
                    ? modelTitleTextComponent
                    : modelTitle && (
                        <Text
                          style={{
                            marginTop: noButtton ? 0 : 5,
                            marginBottom: noButtton || navbarTitle ? 0 : 20,
                            marginHorizontal: 10,
                            fontSize: SIZES.fontLg,
                            color: COLORS.title,
                            fontFamily: "DMSansSemiBold",
                          }}
                        >
                          {modelTitle}
                        </Text>
                      )}

                  <ScrollView
                    style={styles.scrollContainer}
                    showsVerticalScrollIndicator={false}
                  >
                    {children ? (
                      children
                    ) : (
                      <>
                        {currentProductDetails?.map((variant: any) => (
                          <View key={variant.id} style={styles.navItems}>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                              }}
                            >
                              <View style={{ flexDirection: "row", gap: 10 }}>
                                <Image
                                  style={styles.productImage}
                                  source={{ uri: variant.image?.url }}
                                />
                                <View
                                  style={{ gap: 8, justifyContent: "center" }}
                                >
                                  <Text
                                    style={{
                                      // ...FONTWEIGHT.Bold,
                                      fontSize: SIZES.font,
                                      ...FONTS.fontSemiBold,
                                    }}
                                  >
                                    {variant?.title}
                                  </Text>
                                  <Text
                                    style={{
                                      color: COLORS.textBrandName,
                                      fontSize: SIZES.fontXs,
                                      ...FONTS.fontRegular,
                                    }}
                                  >
                                    SKU: {variant.sku || "N/A"}
                                  </Text>
                                  <Text
                                    style={{
                                      ...FONTWEIGHT.Normal,
                                      fontSize: SIZES.fontXs,
                                      ...FONTS.fontRegular,
                                    }}
                                  >
                                    ${variant.price?.amount}
                                  </Text>
                                </View>
                              </View>
                              <View style={styles.quantityContainer}>
                                <TouchableOpacity
                                  onPress={() => handleDecrease(variant.id)}
                                  style={styles.quantityButton}
                                >
                                  <Image
                                    style={{ width: 15, height: 15 }}
                                    source={RemoveIcon}
                                  />
                                </TouchableOpacity>
                                <Text
                                  style={{
                                    fontSize: SIZES.font,
                                    width: 20,
                                    textAlign: "center",
                                  }}
                                >
                                  {quantities[variant.id] || 1} 
                                </Text>

                                <TouchableOpacity
                                  onPress={() => handleIncrease(variant.id)}
                                  style={styles.quantityButton}
                                >
                                  <Image
                                    style={{ width: 15, height: 15 }}
                                    source={AddIcon}
                                  />
                                </TouchableOpacity>
                              </View>
                            </View>
                          </View>
                        ))}
                      </>
                    )}
                  </ScrollView>
                  {children
                    ? isCloseButtonRequired && (
                        <View
                          style={{
                            // marginTop: 10,
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignItems: "center",
                            gap: 10,
                            paddingVertical: 20,
                            paddingHorizontal: clearAllBtn ? 0 : 5,
                            backgroundColor: COLORS.background,
                          }}
                        >
                          {clearAllBtn && (
                            <View
                              style={{
                                gap: 12,
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                width: "100%",
                                // borderWidth:1
                              }}
                            >
                              <Button
                                title={buttonTitle}
                                onPress={onPressLeftBtn}
                                btnRounded={true}
                                color={COLORS.white}
                                onQuantityDecrease={onQuantityDecrease}
                                onQuantityIncrease={onQuantityIncrease}
                                outline={true}
                                style={{
                                  width: clearAllBtn
                                    ? leftIconWidth
                                      ? leftIconWidth
                                      : 160
                                    : 150,
                                  height: 60,
                                }}
                                leftIcon={leftIcon}
                                rightIcon={rightIcon}
                              />
                              <Button
                                title={
                                  rightButtonTitle
                                    ? rightButtonTitle
                                    : ButtonLabel.addToBag
                                }
                                onPress={onPressRightBtn}
                                btnRounded={true}
                                style={{ width: buttonWidth }}
                              />
                            </View>
                          )}
                        </View>
                      )
                    : !planBottomSheet && (
                        <View style={{ marginTop: 10, marginHorizontal: 10 }}>
                          {loading ? (
                            <ActivityIndicator
                              size="large"
                              color={COLORS.primary}
                            />
                          ) : (
                            <Button
                              title={ButtonLabel.addToBag}
                              onPress={async () => {
                                await handleAddToBag();
                              }}
                              btnRounded={true}
                              style={{ width: buttonWidth }}
                            />
                          )}
                        </View>
                      )}
                </Animated.View>
              </TouchableWithoutFeedback>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  bottomNavContainer: {
    backgroundColor: "white",
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "80%",
  },
  scrollContainer: {
    width: "100%",
  },
  modalText: {
    fontSize: SIZES.fontLg,
    ...FONTWEIGHT.Medium,
    marginBottom: 10,
  },
  navItems: {
    gap: 10,
    marginBottom: 15,
  },
  productImage: {
    width: 70,
    height: 70,
    borderRadius: 10,
  },
  quantityContainer: {
    width: 120,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-evenly",
    alignSelf: "center",
    borderWidth: 0.8,
    borderColor: COLORS.darkgray,
    height: 30,
    borderRadius: 8,
    gap: 10,
    marginHorizontal: 20,
  },
  quantityButton: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    // borderWidth:1
  },
});

export default memo(BottomNavModal);
