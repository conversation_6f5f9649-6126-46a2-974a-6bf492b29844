import { View, Text, Image, TouchableOpacity, ScrollView } from "react-native";
import React from "react";
import { COLORS, FONTS, FONTWEIGHT, SIZES } from "../../constants/theme";
import { GlobalStyleSheet } from "../../constants/StyleSheet";
import RedirectIcon from "../../assets/icons/redirectionindicationicon.png";
import { useNavigation } from "@react-navigation/native";
export default function CardSlider({
  products,
  smallCard,
  addBackground,
  searchSuggectionCard = false,
  categoryCards = false,
}: any) {
  const navigation = useNavigation();
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentInset={{ borderWidth: 1 }}
      keyboardShouldPersistTaps="handled"
      scrollEventThrottle={18}
      decelerationRate="fast"
    >
      <View
        style={[
          GlobalStyleSheet.row,
          {
            flexWrap: "nowrap",
            marginHorizontal: !categoryCards && smallCard ? 16 : 0,
            marginRight: smallCard ? 25 : 25,
            // marginHorizontal: addBackground ? 0 : 5,
            gap: 10,
          },
        ]}
      >
        {products.map((data: any, index: any) => {
          const handle = data?.url?.split("/")?.pop();
          return (
            <View
              style={[
                {
                  // marginBottom: addBackground ? 0 : 20,
                  width: smallCard ? 100 : 150,
                  height: smallCard ? 125 : 183,
                  backgroundColor: smallCard ? COLORS.card : COLORS.background,
                  borderRadius: 20,
                },
              ]}
              key={data.image + data.title}
            >
              <TouchableOpacity
                activeOpacity={0.7}
                style={{
                  borderWidth: 1,
                  borderColor: "white",
                  borderRadius: 20,
                  backgroundColor: addBackground
                    ? COLORS.background
                    : COLORS.card,
                }}
                onPress={() => {
                  navigation.navigate("ProductListingPage", {
                    handle: data?.handle ? data.handle : handle ? handle : null,
                    headerBarTitle: data?.title,
                  });
                }}
              >
                <Image
                  style={{
                    width: smallCard ? 100 : 150,
                    height: addBackground ? 70 : smallCard ? 100 : 138,
                    objectFit: "cover",
                    // aspectRatio: smallCard ? null : 2 / 2,
                    borderTopLeftRadius: 15,
                    borderTopRightRadius: 15,
                  }}
                  source={{
                    uri:
                      data?.imageUrl ??
                      "https://media.istockphoto.com/id/1761333789/photo/badminton-shuttlecocks-and-racket-placed-in-the-corner-of-a-synthetic-field.jpg?s=612x612&w=0&k=20&c=3rr4BZqe1rDWsCe6LF_YPCXZe6Um5jizc6d6n96U1Q4=",
                  }}
                />
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-evenly",
                    alignItems: "center",
                    marginVertical: smallCard ? 2 : 10,
                    paddingHorizontal: smallCard ? 8 : 16,
                    width: "100%",
                    // borderWidth:1
                  }}
                >
                  <Text
                    style={{
                      ...FONTS.fontSemiBold,
                      color: COLORS.black,
                      fontSize: SIZES.font,
                      width: "75%",
                      flexWrap: "wrap",
                    }}
                    numberOfLines={1}
                    ellipsizeMode="tail"
                  >
                    {data?.title}
                  </Text>
                  <Image
                    source={RedirectIcon}
                    style={{
                      width: smallCard ? 20 : 25,
                      height: smallCard ? 20 : 25,
                      // marginRight: 10,
                    }}
                  />
                </View>
              </TouchableOpacity>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
}
