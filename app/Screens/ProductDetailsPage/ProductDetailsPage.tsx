import {
  View,
  Text,
  ScrollView,
  SafeAreaView,
  Image,
  Dimensions,
  TouchableOpacity,
  Animated,
  Platform,
  UIManager,
  Easing,
  Alert,
} from "react-native";
import React, { useCallback, useEffect, useRef, useState } from "react";
import Header from "../../layout/Header";
import {
  ButtonLabel,
  COLORS,
  DMSansFONTS,
  FONTS,
  FONTWEIGHT,
  SIZES,
} from "../../constants/theme";
import ScrollIndicator from "../../components/ScrollIndicator/ScrollIndicator";
import Badge from "../../components/Badge/Badge";
import Button from "../../components/Button/Button";
import { white } from "react-native-paper/lib/typescript/styles/themes/v2/colors";
import { useMutation, useQuery } from "@apollo/client";
import {
  GET_CATEGORY_PRODUCTS,
  GET_SINGLE_PRODUCT,
} from "../../api/viewCategoryProductQuery";
import { IMAGES } from "../../constants/Images";
import SizeGuideIcon from "../../assets/icons/size.png";
import BottomSheetNew from "../../components/BottomSheetNew/BottomSheetNew";
import BackArrow from "../../assets/icons/arrowback.png";
import ForwardArrow from "../../assets/icons/arrowforward.png";
import { ActivityIndicator } from "react-native-paper";
import VariantCardsContainer from "../../components/VariantCardsContainer/VariantCardsContainer";
import { useDispatch, useSelector } from "react-redux";
import { ADD_LINES_TO_CART, CREATE_CART } from "../../api/cartQuery";
import { setCartId } from "../../redux/reducer/cartReducer";
import "react-native-gesture-handler";
if (
  Platform.OS === "android" &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
type AccordianProps = {
  navigation?: any;
  menuItems: any;
  styles?: any;
  accordianWithoutImg?: any;
};
export default function ProductDetailsPage({ navigation, route }: any) {
  const params = route?.params;
  const scrollRef = useRef(null);
  const { width, height } = Dimensions.get("window");
  const [modalVisible, setModalVisible] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [filterSortOptions, setFilterSortOptions] = useState(false);
  const [createCart, { loading: createLoading }] = useMutation(CREATE_CART);
  const [addCart, { loading: addLoading }] = useMutation(ADD_LINES_TO_CART);
  const dispatch = useDispatch();
  const { cartId } = useSelector((state: any) => state.cart);
  const [quantity, setQuanitity] = useState(1);
  const screenHeight = Dimensions.get("screen").height;
  if (!params?.handle) return null;

  const { loading, data, error } = useQuery(GET_SINGLE_PRODUCT, {
    variables: { handle: params?.handle },
  });

  const scrollX = useRef(new Animated.Value(0)).current;

  if (error) {
    return <Text>{error?.message}</Text>;
  }

  const product = data?.productByHandle;

  const productData = {
    id: product?.id || "",
    title: product?.title || "",
    handle: product?.handle || "",
    description: product?.description || "",
    images: product?.images?.edges?.map((edge: any) => edge.node) || [],
    variants: product?.variants?.edges?.map((edge: any) => edge.node) || [],
    selectedVariantId: product?.selectedOrFirstAvailableVariant?.id,
    sizes: [],
  };

  const accodianData = [
    {
      title: "Product Decripion",
      description: productData.description,
    },
    {
      title: "Specifications",
      description: productData.description,
    },
    {
      title: "Material & Care",
      description: productData.description,
    },
  ];
  const handleScrollTo = (direction: "left" | "right") => {
    let newIndex = activeIndex + (direction === "left" ? -1 : 1);
    newIndex = Math.max(0, Math.min(productData?.images.length - 1, newIndex)); // Clamp index
    setActiveIndex(newIndex);
    scrollRef.current?.scrollTo({ x: newIndex * width, animated: true });
  };

  const [selectedVariantId, setSelectedVariantId] = useState();
  const handleVariantChange = useCallback(
    (id: string) => {
      const variant = productData.variants.find(
        (variant: any) => id === variant.id
      );
      if (variant) {
        setSelectedVariantId(variant.id);
      }
    },
    [productData, selectedVariantId]
  );

  const handleAddToBag = () => {
    const lines = [{ quantity, merchandiseId: selectedVariantId }];
    if (cartId) {
      addCart({
        variables: {
          cartId,
          lines: lines,
        },
      })
        .then((response: any) => {
          if (response.data.cartLinesAdd.userErrors.length > 0) {
            alert("Error: " + response.data.cartLinesAdd.userErrors[0].message);
          } else {
            Alert.alert("Success", "Added to cart successfully!", [
              {
                text: "OK",
                onPress: () => {
                  const cartId = response.data.cartLinesAdd.cart.id;
                  // console.log("Cart Id: ", cartId);
                  dispatch(setCartId(cartId));
                  navigation.push("MyCart", {
                    cartId,
                    lines,
                  });
                  // closeBottomSheet();
                },
              },
            ]);
            // closeBottomSheet()
          }
        })
        .catch((err) => {
          alert("Something went wrong. Please try again.");
          console.error("Cart addition error:", err);
        });
    } else {
      createCart({
        variables: {
          lines,
        },
      })
        .then((response: any) => {
          if (response.data.cartCreate.userErrors.length > 0) {
            alert("Error: " + response.data.cartCreate.userErrors[0].message);
          } else {
            Alert.alert("Success", "Added to cart successfully!", [
              {
                text: "OK",
                onPress: () => {
                  const cartId = response.data.cartCreate.cart.id;
                  // console.log("Cart Id: ", cartId);
                  dispatch(setCartId(cartId));
                  navigation.push("MyCart", {
                    cartId,
                    lines,
                  });
                  // closeBottomSheet();
                },
              },
            ]);
            // closeBottomSheet()
          }
        })
        .catch((err) => {
          alert("Something went wrong. Please try again.");
          console.error("Cart creation error:", err);
        });
    }
  };

  const handleIncrease = () => {
    setQuanitity((pre) => pre + 1);
  };

  const handleDecrease = () => {
    setQuanitity((pre) => {
      if (pre <= 1) {
        return pre;
      }
      return pre - 1;
    });
  };

  return (
    <SafeAreaView style={{ backgroundColor: COLORS.background, flex: 1 }}>
      <Header
        title={ButtonLabel.productDetail}
        rightIcon={"cart"}
        leftIcon={"back"}
        titleLeft
        paddingLeft
      />
      {loading ? (
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            height: 600,
          }}
        >
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      ) : (
        <>
          <ScrollView showsVerticalScrollIndicator={false}>
            <View
              style={{
                alignItems: "center",
                marginTop: 20,
                marginHorizontal: 10,
              }}
            >
              <Badge
                title={params?.badge ?? "15 OFF"}
                color={COLORS.badgeBackgroundColor}
                size="md"
                style={{
                  position: "absolute",
                  zIndex: 10000,
                  borderRadius: 8,
                  marginTop: 5,
                  marginHorizontal: 5,
                  left: 10,
                }}
              />
              <ScrollView
                horizontal
                pagingEnabled
                ref={scrollRef}
                showsHorizontalScrollIndicator={false}
                scrollEventThrottle={16}
                contentContainerStyle={{
                  width: width * productData?.images.length,
                }}
                onScroll={Animated.event(
                  [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                  {
                    useNativeDriver: false,
                    listener: (event: any) => {
                      const xOffset = event.nativeEvent.contentOffset.x;
                      const newIndex = Math.round(xOffset / width);
                      setActiveIndex(newIndex);
                    },
                  }
                )}
              >
                {productData?.images.map((img: any, index: any) => {
                  return (
                    <>
                      <Image
                        key={index}
                        source={{ uri: img?.src }}
                        style={{
                          width: width - 20,
                          height: 380,
                          objectFit: "contain",
                          borderRadius: 15,
                        }}
                      />
                    </>
                  );
                })}
              </ScrollView>
              <ScrollIndicator
                images={productData?.images}
                activeIndex={activeIndex}
                styles={{ top: 340, marginTop: 20 }}
              />
              <TouchableOpacity
                onPress={() => {
                  setModalVisible(true);
                }}
                style={{
                  flexDirection: "row",
                  marginTop: 10,
                  position: "absolute",
                  top: 300,
                  right: 10,
                  backgroundColor: "white",
                  padding: 10,
                  borderRadius: 20,
                }}
              >
                <Image source={IMAGES.zoom} style={{ width: 25, height: 25 }} />
              </TouchableOpacity>
            </View>
            <View style={{ width: "100%" }}>
              <View
                style={{
                  gap: 10,
                  borderTopLeftRadius: 30,
                  borderTopRightRadius: 30,
                  zIndex: 10000,
                  backgroundColor: COLORS.backgroundColor,
                  padding: 5,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <View>
                    <Text
                      style={{
                        color: COLORS.textBrandName,
                        marginHorizontal: 10,
                        marginTop: 10,
                        ...FONTS.fontSemiBoldPop,
                      }}
                    >
                      Yonex
                    </Text>
                  </View>
                  <View>
                    <Image
                      source={IMAGES.share}
                      style={{
                        width: 24,
                        height: 24,
                        marginHorizontal: 15,
                        marginTop: 6,
                      }}
                    />
                  </View>
                </View>
                <View style={{}}>
                  <Text
                    style={{
                      fontSize: SIZES.h22,
                      lineHeight: 25,
                      marginHorizontal: 10,
                      fontFamily: "DMSansMedium",
                      // ...FONTWEIGHT.Normal
                    }}
                  >
                    {productData?.title}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    gap: 10,
                    alignItems: "center",
                  }}
                >
                  <View style={{}}>
                    <View style={{}}>
                      <Text
                        style={{
                          fontSize: SIZES.h22,
                          marginHorizontal: 10,
                          fontFamily: "DMSansExtraBold",
                        }}
                      >
                        $
                        {productData.variants.find(
                          (variant) => variant.id === selectedVariantId
                        )?.price.amount || "N/A"}
                      </Text>

                      <Text
                        style={{
                          marginHorizontal: 10,
                          fontFamily: "DMSansRegular",
                          fontSize: SIZES.fontXxs,
                        }}
                      >
                        {ButtonLabel.inclAllTaxes}
                      </Text>
                    </View>
                  </View>
                  <View>
                    <Text
                      style={{
                        height: 30,
                        color: COLORS.gray,
                        textDecorationLine: "line-through",
                        fontFamily: "DMSansRegular",
                        fontSize: SIZES.font,
                      }}
                    >
                      $186.00
                    </Text>
                  </View>
                  <View>
                    <Text
                      style={{
                        height: 28,
                        ...FONTS.fontXs,
                        ...FONTWEIGHT.SemiBold,
                        color: COLORS.red,
                        fontFamily: "DMSansRegular",
                      }}
                    >{`(30% Off)`}</Text>
                  </View>
                </View>
                <View style={{ marginTop: 20 }}>
                  <View style={{ flexDirection: "row", marginHorizontal: 10 }}>
                    <Text
                      style={{ fontFamily: "DMSansBold", fontSize: SIZES.font }}
                    >
                      Choose Color
                    </Text>
                    <Text
                      style={{
                        fontWeight: "400",
                        color: "#1C1B1B",
                        fontSize: SIZES.font,
                      }}
                    >{`  (${productData?.variants.length} Colors)`}</Text>
                  </View>

                  <View
                    style={{
                      marginTop: 20,
                      marginHorizontal: 10,
                    }}
                  >
                    <ScrollView
                      horizontal
                      ref={scrollRef}
                      showsHorizontalScrollIndicator={false}
                      scrollEventThrottle={16}
                    >
                      <VariantCardsContainer
                        variants={productData.variants}
                        selectedVariantId={selectedVariantId}
                        handleVariantChange={handleVariantChange}
                      />

                      {/* <View style={{ gap: 8, flexDirection: "row" }}>
                        {productData?.variants.map(
                          (variant: any, index: any) => {
                            console.log("single varient data", variant);
                            return (
                              <TouchableOpacity
                                key={index}
                                style={{
                                  alignItems: "center",
                                  // marginHorizontal: 5,
                                  borderWidth: 0.1,
                                  borderColor: COLORS.gray,
                                  // padding: 10,
                                  borderRadius: 10,
                                  height: 100,
                                }}
                              >
                                <Image
                                  source={{ uri: variant?.image.src }}
                                  style={{
                                    width: 80,
                                    height: 80,
                                    borderWidth: 1,
                                    aspectRatio: 1 / 1,
                                    objectFit: "contain",
                                    borderRadius: 15,
                                  }}
                                />
                                <View>
                                  <Text
                                    style={{
                                      ...FONTS.font,
                                      ...FONTWEIGHT.SemiBold,
                                    }}
                                  >
                                    {variant?.title}
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            );
                          }
                        )}
                      </View> */}
                    </ScrollView>
                  </View>
                </View>
                {productData.sizes.length && (
                  <View style={{ marginTop: 10, marginHorizontal: 10 }}>
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      <Text
                        style={{
                          fontFamily: "DMSansBold",
                          fontSize: SIZES.font,
                        }}
                      >
                        Choose Sizes
                      </Text>
                      <View style={{ flexDirection: "row", gap: 3 }}>
                        <Image
                          source={SizeGuideIcon}
                          style={{ width: 14, height: 14, marginTop: 1 }}
                        />
                        <Text
                          style={{
                            fontFamily: "DMSansSemiBold",
                            fontSize: SIZES.fontXs,
                            textDecorationLine: "underline",
                          }}
                        >
                          Size Guide
                        </Text>
                      </View>
                    </View>
                    <View
                      style={{
                        flexDirection: "row",
                        gap: 10,
                        flexWrap: "wrap",
                      }}
                    >
                      {productData?.images
                        .slice(0, 6)
                        .map((_: any, index: any) => {
                          const cross = false;
                          return (
                            <TouchableOpacity
                              style={{
                                marginTop: 10,
                                borderWidth: 1,
                                borderRadius: 10,
                                paddingVertical: 10,
                                width: 80,
                                opacity: cross ? 0.2 : 1,
                                backgroundColor:
                                  activeIndex === index
                                    ? COLORS.black
                                    : COLORS.white,
                              }}
                              disabled={cross}
                            >
                              <Text
                                style={{
                                  textAlign: "center",
                                  ...FONTS.font,
                                  color:
                                    activeIndex === index
                                      ? COLORS.white
                                      : COLORS.black,
                                }}
                              >
                                3U / G6
                              </Text>
                              {cross && (
                                <View
                                  style={{
                                    position: "absolute",
                                    transform: [{ rotateZ: "155deg" }],
                                    bottom: 20,
                                    right: 0,
                                    height: 0,
                                    width: 82,
                                    borderWidth: 0.3,
                                  }}
                                ></View>
                              )}
                            </TouchableOpacity>
                          );
                        })}
                    </View>
                  </View>
                )}
                <View style={{ marginTop: 10, marginHorizontal: 10 }}>
                  {accodianData.map((accordion) => {
                    return (
                      <Accordion
                        menuItems={accordion}
                        accordianWithoutImg={true}
                        key={accordion.title}
                      />
                    );
                  })}
                </View>
              </View>
            </View>
            <View style={{ justifyContent: "center", flexDirection: "row" }}>
              <View
                style={{
                  marginVertical: 40,
                  backgroundColor: "#00838B",
                  width: "90%",
                  borderRadius: 15,
                }}
              >
                <View
                  style={{ flexDirection: "row", justifyContent: "center" }}
                >
                  <Image
                    source={{
                      uri: "https://m.media-amazon.com/images/I/514Gy2Ggs9L._AC_UF1000,1000_QL80_.jpg",
                    }}
                    style={{
                      width: "100%",
                      height: 200,
                      objectFit: "cover",
                      borderTopLeftRadius: 15,
                      borderTopRightRadius: 15,
                    }}
                  />
                </View>
                <View
                  style={{
                    padding: 5,
                    gap: 10,
                    marginHorizontal: 10,
                    marginVertical: 10,
                  }}
                >
                  <View>
                    <Text
                      style={{
                        color: COLORS.white,
                        ...FONTWEIGHT.SemiBold,
                        ...FONTS.fontMedium,
                        fontSize: SIZES.fontLg,
                      }}
                    >
                      Yonex
                    </Text>
                  </View>
                  <View>
                    <Text
                      style={{
                        fontSize: 25,
                        fontFamily: "DMSansRegular",
                        color: COLORS.white,
                        ...FONTWEIGHT.Normal,
                      }}
                    >
                      Lorem Ipsum
                    </Text>
                  </View>
                  <View>
                    <Text
                      style={{
                        ...FONTS.font,
                        color: COLORS.white,
                        textAlign: "justify",
                        lineHeight: 20,
                      }}
                    >
                      Lorem ipsum dolor sit amet consectetur adipisicing elit.
                      Accusantium explicabo corrupti ipsa magnam, officiis
                      ratione tempore quasi inventore suscipit laborum enim amet
                      itaque vitae voluptatibus exercitationem .
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </ScrollView>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-around",
              alignItems: "center",
              backgroundColor: COLORS.white,
              borderTopColor: COLORS.gray,
              height: 90,
              paddingBottom: 10,
              // marginVertical: 15,
            }}
          >
            <TouchableOpacity
              style={{
                flexDirection: "row",
                alignItems: "center",

                gap: 10,
                marginTop: 10,
              }}
              onPress={() => {
                setFilterSortOptions(true);
              }}
            >
              <Button
                title={quantity}
                onPress={() => {}}
                on
                btnRounded={true}
                color={COLORS.white}
                outline={true}
                style={{ width: 150, height: 60 }}
                leftIcon={"-"}
                onQuantityDecrease={handleDecrease}
                onQuantityIncrease={handleIncrease}
                rightIcon={"+"}
              />
              <Button
                title={
                  // rightButtonTitle
                  //   ? rightButtonTitle
                  //   : ButtonLabel.addToBag

                  ButtonLabel.addToBag
                }
                onPress={handleAddToBag}
                btnRounded={true}
                style={{ width: 230 }}
              />
            </TouchableOpacity>
          </View>
          <BottomSheetNew
            modalVisible={modalVisible}
            currentProductDetails={[]}
            setModalVisible={setModalVisible}
            buttonWidth={200}
            headerEnabled={false}
            clearAllBtn={true}
            planBottomSheet={true}
            height={screenHeight}
            navbarTitle=""
            isCloseButtonRequired={false}
            isBackBtnRequired={true}
          >
            <View
              style={{
                position: "relative",
                backgroundColor: COLORS.background,
                height: screenHeight - 340,
              }}
            >
              <ScrollView
                horizontal
                pagingEnabled
                ref={scrollRef}
                showsHorizontalScrollIndicator={false}
                scrollEventThrottle={16}
                contentContainerStyle={{
                  width: width * productData?.images.length,
                }}
                onScroll={Animated.event(
                  [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                  {
                    useNativeDriver: false,
                    listener: (event: any) => {
                      const xOffset = event.nativeEvent.contentOffset.x;
                      const newIndex = Math.round(xOffset / width);
                      setActiveIndex(newIndex);
                    },
                  }
                )}
              >
                {productData?.images.map((img: any, index: any) => (
                  <Image
                    key={index}
                    source={{ uri: img?.src }}
                    style={{
                      width: width,
                      height: height * 0.9, // 60% of screen height
                      objectFit: "contain",
                      borderRadius: 15,
                    }}
                  />
                ))}
              </ScrollView>
              <View>
                {activeIndex > 0 && (
                  <TouchableOpacity
                    onPress={() => handleScrollTo("left")}
                    style={{
                      position: "relative",
                      left: 10,
                      bottom: 210,
                      marginHorizontal: 12,
                    }}
                  >
                    <Image
                      source={BackArrow}
                      style={{ width: 13, height: 23 }}
                    />
                  </TouchableOpacity>
                )}
                {activeIndex < productData?.images.length - 1 && (
                  <TouchableOpacity
                    onPress={() => handleScrollTo("right")}
                    style={{
                      position: "relative",
                      left: 380,
                      bottom: 230,
                      zIndex: 100,
                      marginHorizontal: 12,
                    }}
                  >
                    <Image
                      source={ForwardArrow}
                      style={{ width: 13, height: 23 }}
                    />
                  </TouchableOpacity>
                )}
              </View>
            </View>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-around",
                alignItems: "center",
                backgroundColor: COLORS.card,
                // borderTopWidth: 1,
                borderTopColor: COLORS.gray,
                height: 120,
                marginTop: 140,
              }}
            >
              <View
                style={{
                  marginHorizontal: 10,
                }}
              >
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  scrollEventThrottle={16}
                >
                  <View
                    style={{
                      gap: 11,
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    {productData?.images.map((img: any, index: any) => (
                      <>
                        <TouchableOpacity
                          key={index}
                          onPress={() => {
                            scrollRef.current?.scrollTo({
                              x: index * width,
                              animated: true,
                            });
                            setActiveIndex(index);
                          }}
                          style={{
                            alignItems: "center",
                            // marginHorizontal: 5,
                            borderWidth: 0.1,
                            borderColor: COLORS.gray,
                            // padding: 10,
                            // borderRadius: 4,
                            height: 100,
                          }}
                        >
                          <Image
                            source={{ uri: img?.src }}
                            style={{
                              width: 90,
                              height: 90,
                              borderWidth: 1,
                              aspectRatio: 1 / 1,
                              borderColor: COLORS.lightgray,
                              objectFit: "cover",
                              borderRadius: 4,
                            }}
                          />
                        </TouchableOpacity>
                      </>
                    ))}
                  </View>
                </ScrollView>
              </View>
            </View>
          </BottomSheetNew>
        </>
      )}
    </SafeAreaView>
  );
}

export const Accordion = ({
  menuItems,
  navigation,
  styles,
}: AccordianProps) => {
  const [isActive, setIsActive] = useState(false);
  const animation = useRef(new Animated.Value(0)).current;

  const toggleAccordion = () => {
    Animated.timing(animation, {
      toValue: isActive ? 0 : 1,
      duration: 400, // smoother open/close speed
      easing: Easing.inOut(Easing.ease), // smooth easing
      useNativeDriver: false,
    }).start();

    setIsActive(!isActive);
  };
  const animatedHeight = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 100],
  });

  return (
    <View>
      <TouchableOpacity
        onPress={() => {
          toggleAccordion();
        }}
        style={
          styles
            ? { ...styles }
            : {
                flexDirection: "row",
                backgroundColor: COLORS.backgroundColor,
                // borderRadius: 10,
                width: "100%",
                borderWidth: 1,
                borderColor: "white",
                borderBottomColor: COLORS.gray,
              }
        }
      >
        <View style={{ flexDirection: "row", width: "100%" }}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
              marginVertical: 16,
              borderWidth: 1,
              paddingTop: 5,
              paddingBottom: 16,
              borderBottomColor: "#EEEEEE",
              borderTopColor: COLORS.white,
              borderLeftColor: COLORS.white,
              borderRightColor: COLORS.white,
            }}
          >
            <Text
              style={{
                ...FONTS.fontLg,
                ...FONTS.fontBold,
                marginHorizontal: 4,
              }}
            >
              {menuItems.title}
            </Text>
            <Image
              source={isActive ? IMAGES.minus : IMAGES.plus}
              style={{
                width: 15,
                height: 15,
                objectFit: "contain",
              }}
            />
          </View>
        </View>
      </TouchableOpacity>

      <Animated.View
        style={{
          height: animatedHeight,
          overflow: "hidden",
          backgroundColor: COLORS.backgroundColor,
          marginHorizontal: 5,
          // borderRadius: 10,
        }}
      >
        <ScrollView contentContainerStyle={{ width: "100%" }}>
          <View>
            <Text
              style={{
                ...FONTS.font,
                ...FONTWEIGHT.SemiBold,
                color: COLORS.gray,
                textAlign: "justify",
              }}
            >
              {menuItems.description}
            </Text>
          </View>
        </ScrollView>
      </Animated.View>
    </View>
  );
};
