import { useTheme } from '@react-navigation/native';
import React from 'react';
import { View, Text, SafeAreaView, Image, TouchableOpacity, SectionList, ScrollView, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import {  FONTS, COLORS } from '../../constants/theme';

import ListItem from '../../components/list/ListItem';
import { IMAGES } from '../../constants/Images';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '../../Navigations/RootStackParamList';
import "react-native-gesture-handler";

const btnData = [
    {
        title: "Your Order",
        navigate: 'Myorder',
    },
    {
        title: "Wishlist",
        navigate: 'Wishlist',
    },
    {
        title: "Coupons",
        navigate: 'Coupons',
    },
    {
        title: "Track Order",
        navigate: 'Trackorder',
    },
]


const ListwithiconData = [
    {
        title: 'Account Settings',
        data: [
            {
                icon: IMAGES.user2,
                title: "Edit Profile",
                navigate: 'EditProfile'
            },
            {
                icon: IMAGES.card2,
                title: "Saved Cards & Wallet",
                navigate: 'Payment'
            },
            {
                icon: IMAGES.map2,
                title: "Saved Addresses",
                navigate: 'SavedAddresses'
            },
            {
                icon: IMAGES.translation,
                title: "Select Language",
                navigate: 'Language'
            },
            {
                icon: IMAGES.bell2,
                title: "Notifications Settings",
                navigate: 'Notification'
            },
        ],
    },
    {
        title: 'My Activity',
        data: [
            {
                icon: IMAGES.star,
                title: "Reviews",
                navigate: 'WriteReview'
            },
            {
                icon: IMAGES.comment,
                title: "Questions & Answers",
                navigate: 'Questions'
            },
        ],
    },

];

type ProfileScreenProps = StackScreenProps<RootStackParamList, 'Profile'>;

const Profile = ({ navigation } : ProfileScreenProps) => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    return (

        <SafeAreaView style={{ backgroundColor: colors.background, flex: 1, }}>
            {theme.dark ?
                null
                :
                <LinearGradient colors={['#FEEB9D', '#FFFAF3']}
                    style={{ width: '100%', height: 230, top: 0, position: 'absolute' }}
                >
                </LinearGradient>
            }
            <View style={[GlobalStyleSheet.container, { flex: 1 }]}>
                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                        <Image
                            style={{ height: 22, width: 20, resizeMode: 'contain' }}
                            source={IMAGES.logo}
                        />
                        <Text style={{ ...FONTS.fontSemiBold, fontSize: 24, color: colors.title }}>Pixio</Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <TouchableOpacity
                            onPress={() => navigation.navigate('Notification')}
                            style={{ padding: 5 }}
                        >
                            <Image
                                style={[GlobalStyleSheet.image, { tintColor: colors.title }]}
                                source={IMAGES.bell}
                            />
                        </TouchableOpacity>
                        <View style={[GlobalStyleSheet.notification, { position: 'absolute', right: 30, bottom: 15 }]}>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 10, color: COLORS.white }}>14</Text>
                        </View>
                        <TouchableOpacity
                            onPress={() => navigation.navigate('Search')}
                            style={{ padding: 5 }}
                        >
                            <Image
                                style={[GlobalStyleSheet.image, { tintColor: colors.title }]}
                                source={IMAGES.search}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10, paddingTop: 20, paddingBottom: 30 }}>
                    <View style={{ height: 45, width: 45, borderRadius: 50, backgroundColor: COLORS.white, alignItems: 'center', justifyContent: 'center' }}>
                        <Image
                            style={{ height: 40, width: 40, borderRadius: 50 }}
                            source={IMAGES.small2}
                        />
                    </View>
                    <Text style={{ ...FONTS.fontRegular, fontSize: 24, color: colors.title }}>Hello,<Text style={{ ...FONTS.fontSemiBold }}> Navdeep</Text></Text>
                </View>
                <View style={[GlobalStyleSheet.row]}>
                    {btnData.map((data:any, index:any) => {
                        return (
                            <View key={index} style={[GlobalStyleSheet.col50, { marginBottom: 15 }]}>
                                <View
                                    style={[{
                                        shadowColor: "#000",
                                        shadowOffset: {
                                            width: 2,
                                            height: 2,
                                        },
                                        shadowOpacity: .1,
                                        shadowRadius: 5,
                                    }, Platform.OS === "ios" && {
                                        backgroundColor: colors.card,
                                        borderRadius:35
                                    }]}
                                >
                                    <TouchableOpacity
                                        onPress={() => navigation.navigate(data.navigate)}
                                        style={{
                                            height: 48,
                                            backgroundColor: colors.card,
                                            //width: 180,
                                            borderRadius: 35,
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}
                                    >
                                        <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: colors.title }}>{data.title}</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        )
                    })}
                </View>
                <View style={{ marginHorizontal: -15, marginTop: 20, flex: 1 }}>
                    <SectionList
                        sections={ListwithiconData}
                        keyExtractor={(item:any, index:any) => item + index}
                        renderItem={({ item } : any) => (
                            <ListItem
                                icon={
                                    <Image
                                        style={{
                                            height: 20,
                                            width: 20,
                                            tintColor: colors.title,
                                            resizeMode: 'contain',
                                        }}
                                        source={item.icon}
                                    />
                                }
                                title={item.title}
                                onPress={() => navigation.navigate(item.navigate)}
                            />
                        )}
                        renderSectionHeader={({ section: { title } }) => (
                            <Text style={{ ...FONTS.fontMedium, fontSize: 20, color: colors.title, paddingLeft: 20, paddingBottom: 10, paddingTop: 20,backgroundColor:colors.background }}>{title}</Text>
                        )}
                    />
                </View>
            </View>
        </SafeAreaView>
    )
}

export default Profile