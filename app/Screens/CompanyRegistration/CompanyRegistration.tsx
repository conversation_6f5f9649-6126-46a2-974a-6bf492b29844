import React, { useEffect, useLayoutEffect, useState } from "react";
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Image,
  Alert,
  Linking,
  ActivityIndicator,
} from "react-native";
import HeaderStyle4 from "../../components/Headers/HeaderStyle4";
import StepIndicator from "react-native-step-indicator";
import { companyDetails } from "../../constants/companyDetails";
import { FONTS, SORA } from "../../constants/theme";
import { Picker } from "@react-native-picker/picker";
import ChevronICon from "../../assets/icons/chevron.png";
import * as DocumentPicker from "expo-document-picker";
import { Feather } from "@expo/vector-icons";

interface FormData {
  [key: string]: string | FileData;
}

interface FileData {
  uri: string;
  name: string;
  type: string;
}

interface StepDetails {
  [key: string]: string;
}

interface ApiResponse {
  success: boolean;
  data?: {
    currentStep: number;
    details: {
      [key: string]: StepDetails;
    };
  };
}

const CompanyRegistration = ({ navigation }: any) => {
  const [currentPosition, setCurrentPosition] = useState(0);
  const [formData, setFormData] = useState<FormData>({});
  const [finalReviewFields, setFinalReviewFields] = useState<FormData>({});
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchAndFillFormData();
  }, []);

  // useLayoutEffect(() => {
  //   if (currentPosition == 4) {
  //     navigation.navigate("ApplicationSubmitted");
  //   }
  // },[currentPosition]);

  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleFileUpload = async (field: string) => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: "*/*",
        copyToCacheDirectory: true,
      });

      if (
        result.canceled === false &&
        result.assets &&
        result.assets.length > 0
      ) {
        const file = result.assets[0];
        setFormData({
          ...formData,
          [field]: {
            uri: file.uri,
            name: file.name || "file",
            type: file.mimeType || "application/octet-stream",
          },
        });
      }
    } catch (error) {
      console.error("Error picking document:", error);
      Alert.alert("Error", "Failed to pick document");
    }
  };

  const removeFile = (field: string) => {
    const newFormData = { ...formData };
    delete newFormData[field];
    setFormData(newFormData);
  };

  const fetchAndFillFormData = async () => {
    try {
      const response = await fetch(
        "https://b9cp0re1s3.execute-api.ap-south-1.amazonaws.com/api/frontend/customer/7530882072676",
        {
          method: "GET",
        }
      );

      const result: ApiResponse = await response.json();
      console.log("Fetched data:", result);

      if (result?.data?.details) {
        const currentStep = result.data.currentStep;
        const details = result.data.details;

        // Fill final review fields
        fillFinalReviewFields(details);

        // Create a map of all form data
        const populatedFormData: FormData = {};

        // Process each step's data
        Object.entries(details).forEach(([stepKey, stepData]) => {
          Object.entries(stepData).forEach(([fieldKey, value]) => {
            if (value !== undefined && value !== null && value !== "") {
              populatedFormData[fieldKey] = value;
            }
          });
        });

        // Update form data
        setFormData(populatedFormData);

        // Only set the initial position on first load
        if (isInitialLoad) {
          // If we have all required data for step 3 (files), move to step 4
          if (currentStep === 3) {
            const hasRequiredFiles = checkRequiredFiles(populatedFormData);
            if (hasRequiredFiles) {
              setCurrentPosition(3); // Move to step 4 (index 3)
            } else {
              setCurrentPosition(2); // Stay on step 3 (index 2)
            }
          } else {
            setCurrentPosition(currentStep - 1); // For other steps, use the API response
          }
          setIsInitialLoad(false);
        }
        // Don't change position if not initial load (when going back)
      }
    } catch (error) {
      console.error("Error fetching form data:", error);
      Alert.alert("Error", "Something went wrong while fetching your data.");
    }
  };

  // Helper function to check if all required files are present
  const checkRequiredFiles = (data: FormData): boolean => {
    if (data.entityType === "Limited Liability Company") {
      const requiredFiles = [
        "form9",
        "form24",
        "form49",
        "memArticle",
        "icCard",
      ];
      return requiredFiles.every((file) => data[file]);
    } else if (data.entityType === "Sole Proprietor") {
      const requiredFiles = ["formD", "form1", "icCard"];
      return requiredFiles.every((file) => data[file]);
    }
    return false;
  };

  const isStepValid = () => {
    // For step 4 (Review & Submit), always return true since all data should be valid
    if (currentPosition === 3) {
      return true;
    }

    const currentStepFields = companyDetails.steps[currentPosition].input;

    // Special handling for step 3 (file uploads)
    if (currentPosition === 2) {
      if (!formData.entityType) return false;
      return checkRequiredFiles(formData);
    }

    // For other steps, check required fields
    return currentStepFields.every((field) => {
      if (!field.isMandatory) return true;

      // Check for value in formData using all possible field identifiers
      const value =
        formData[field.valueField] ||
        formData[field.fieldName] ||
        formData[field.fieldLabel];

      // If value exists and is not empty, or if it's a file object, consider it valid
      return (
        (value && (typeof value === "string" ? value.trim() !== "" : true)) ||
        (typeof value === "object" && "uri" in value)
      );
    });
  };

  const fillFinalReviewFields = (details: { [key: string]: StepDetails }) => {
    const allFields: FormData = {};

    Object.entries(details).forEach(([stepKey, stepData]) => {
      Object.entries(stepData).forEach(([fieldKey, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          allFields[fieldKey] = value;
        }
      });
    });

    setFinalReviewFields(allFields);
  };

  const handleNext = async () => {
    if (currentPosition < companyDetails.steps.length - 1) {
      try {
        setIsLoading(true);
        // Get the current step's input fields
        const currentStepFields = companyDetails.steps[currentPosition].input;

        // Create a clean payload with all required fields
        const stepData: { [key: string]: any } = {
          // Include all required fields with default empty values
          name: formData.name || "",
          companyName: formData.companyName || "",
          email: formData.email || "",
          phone: formData.phone || "",
          tin: formData.tin || "",
          brn: formData.brn || "",
          nricNumber: formData.nricNumber || "",
          obrn: formData.obrn || "",
          sst: formData.sst || "",
          country: formData.country || "",
          state: formData.state || "",
          city: formData.city || "",
          pincode: formData.pincode || "",
          businessAddress: formData.businessAddress || "",
          businessDescription: formData.businessDescription || "",
          msicCodes: formData.msicCodes || "",
        };

        // Map the current step's fields to the payload
        currentStepFields.forEach((field) => {
          const fieldKey = field.valueField;
          if (fieldKey && formData[fieldKey]) {
            stepData[fieldKey] = formData[fieldKey];
          }
        });

        let payload: any;
        let headers: any = {
          "Content-Type": "application/json",
        };

        if (currentPosition === 2) {
          // Step 3 - Handle file uploads
          const formDataToSend = new FormData();

          // Add entity type
          formDataToSend.append("entityType", formData.entityType as string);

          // Create details object with file information
          const details: { [key: string]: any } = {
            ...stepData,
            entityType: formData.entityType,
          };

          let hasFiles = false;

          // Handle files based on company type
          if (formData.entityType === "Limited Liability Company") {
            const fileFields = [
              "form9",
              "form24",
              "form49",
              "memArticle",
              "lan",
              "bankStatement",
              "icCard",
            ];
            fileFields.forEach((field) => {
              const fileData = formData[field];
              if (fileData) {
                hasFiles = true;
                if (typeof fileData === "string") {
                  // If it's a URL from API, add it to both FormData and details
                  const fileName = fileData.split("/").pop() || "file.pdf";
                  formDataToSend.append(field, {
                    uri: fileData,
                    type: "application/pdf",
                    name: fileName,
                  } as any);
                  details[field] = fileName;
                } else if (fileData.uri) {
                  // If it's a new file upload, add it to FormData
                  formDataToSend.append(field, {
                    uri: fileData.uri,
                    type: fileData.type,
                    name: fileData.name,
                  } as any);
                  details[field] = fileData.name;
                }
              }
            });
          } else if (formData.entityType === "Sole Proprietor") {
            const fileFields = ["formD", "form1", "icCard"];
            fileFields.forEach((field) => {
              const fileData = formData[field];
              if (fileData) {
                hasFiles = true;
                if (typeof fileData === "string") {
                  // If it's a URL from API, add it to both FormData and details
                  const fileName = fileData.split("/").pop() || "file.pdf";
                  formDataToSend.append(field, {
                    uri: fileData,
                    type: "application/pdf",
                    name: fileName,
                  } as any);
                  details[field] = fileName;
                } else if (fileData.uri) {
                  // If it's a new file upload, add it to FormData
                  formDataToSend.append(field, {
                    uri: fileData.uri,
                    type: fileData.type,
                    name: fileData.name,
                  } as any);
                  details[field] = fileData.name;
                }
              }
            });
          }

          // Check if we have any files (either URLs or new uploads)
          if (!hasFiles) {
            Alert.alert(
              "Error",
              "Please ensure you have at least one document (either existing or new upload)"
            );
            return;
          }

          // Stringify the details object and append to FormData
          formDataToSend.append("details", JSON.stringify(details));
          formDataToSend.append("currentStep", "3");

          payload = formDataToSend;
          // Remove Content-Type header for FormData
          headers = {
            Accept: "application/json",
          };
        } else {
          // Step 1 and 2 - Regular JSON payload
          payload = {
            currentStep: currentPosition + 1,
            details: JSON.stringify(stepData),
          };
        }

        console.log("Sending payload:", payload);

        // Send the data to the API
        const response = await fetch(
          "https://b9cp0re1s3.execute-api.ap-south-1.amazonaws.com/api/frontend/customer/7530882072676",
          {
            method: "POST",
            headers: headers,
            body: currentPosition === 2 ? payload : JSON.stringify(payload),
          }
        );

        const result = await response.json();
        console.log("API Response:", result);

        // Check if the response indicates success
        if (result.status === "success" || result.success) {
          // Move to next step sequentially
          setCurrentPosition(currentPosition + 1);

          // Fetch updated data after successful submission
          await fetchAndFillFormData();
        } else {
          // Handle error response
          const errorMessage =
            result.errors?.[0]?.message ||
            result.message ||
            "Failed to save form data. Please try again.";
          console.error("API Error:", result);
          Alert.alert("Error", errorMessage);
        }
      } catch (error) {
        console.error("Error saving form data:", error);
        Alert.alert("Error", "Something went wrong while saving your data.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleBack = () => {
    if (currentPosition > 0) {
      setCurrentPosition(currentPosition - 1);
    }
  };

  const renderStep3Content = () => {
    return (
      <>
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Select Company Type *</Text>
          <Picker
            selectedValue={
              (formData.entityType as string) || "Select type of your company"
            }
            onValueChange={(value) => handleInputChange("entityType", value)}
            style={{ borderWidth: 1, borderColor: "red" }}
            dropdownIconColor="white"
          >
            <Picker.Item
              label="Select type of your company"
              value="Select type of your company"
              enabled={false}
            />
            <Picker.Item
              label="Limited Liability Company"
              value="Limited Liability Company"
            />
            <Picker.Item label="Sole Proprietor" value="Sole Proprietor" />
          </Picker>
          <Image
            source={ChevronICon}
            style={{
              width: 24,
              height: 24,
              position: "absolute",
              top: 40,
              right: 10,
            }}
          />
        </View>

        {formData.entityType === "Limited Liability Company" && (
          <>
            {renderFileUploadField(
              "form9",
              "Form 9 Business Registration form",
              true
            )}
            {renderFileUploadField(
              "form24",
              "Form 24 Allotment of Shares",
              true
            )}
            {renderFileUploadField(
              "form49",
              "Form 49 Directors Particulars and Company Secretory Info",
              true
            )}
            {renderFileUploadField(
              "memArticle",
              "Memorandom of Articles M&A",
              true
            )}
            {renderFileUploadField("lan", "Latest Annual Returns", false)}
            {renderFileUploadField(
              "bankStatement",
              "Last 3 Month Bank Statements",
              false
            )}
            {renderFileUploadField(
              "icCard",
              "Identity Cards of Directors-IC Card",
              false
            )}
          </>
        )}

        {formData.entityType === "Sole Proprietor" && (
          <>
            {renderFileUploadField(
              "formD",
              "Form D from ROC(SSM) -Peninsular Dealer",
              true
            )}
            {renderFileUploadField(
              "form1",
              "Form 1 and Biz registration certificate (Sabah Sarawak)",
              true
            )}
            {renderFileUploadField(
              "icCard",
              "Identity Cards of Directors-IC Card",
              true
            )}
          </>
        )}
      </>
    );
  };

  const renderFileUploadField = (
    field: string,
    label: string,
    isMandatory: boolean
  ) => {
    const fileData = formData[field] as FileData;
    return (
      <View key={field} style={styles.fileUploadContainer}>
        <Text style={styles.inputLabel}>
          {label}
          {isMandatory ? "*" : ""}
        </Text>
        <TouchableOpacity
          style={styles.uploadButton}
          onPress={() => handleFileUpload(field)}
        >
          <Image
            source={{
              uri: "https://cdn.shopify.com/s/files/1/0741/2259/2535/files/gallery-export.png?v=1712903122",
            }}
            style={{ width: 24, height: 24 }}
          />
          <Text style={styles.uploadText}>Upload File</Text>
        </TouchableOpacity>

        {fileData && (
          <View style={styles.filePreview}>
            <View style={styles.filePreviewRow}>
              <View style={styles.fileInfoContainer}>
                <Feather name="file" size={20} color="#1E60AE" />
                <Text
                  style={styles.fileName}
                  numberOfLines={1}
                  ellipsizeMode="tail"

                >
                  {fileData?.name ?? fileData.split("/").pop()}
                </Text>
              </View>
              <TouchableOpacity onPress={() => removeFile(field)}>
                <Feather name="x" size={20} color="gray" />
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderStep4Content = () => {
    return (
      <View style={styles.reviewContainer}>
        {/* Display all text fields from all steps */}
        {companyDetails.steps.slice(0, 3).map((step) =>
          step.input.map((field) => {
            // Get value from formData using valueField, fieldName, or fieldLabel
            const value =
              formData[field.valueField] ||
              formData[field.fieldName] ||
              formData[field.fieldLabel];

            // Skip if no value or if it's a file object
            if (!value || (typeof value === "object" && "uri" in value))
              return null;

            return (
              <View key={field.valueField} style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                  {field.label}
                  {field.isMandatory ? "*" : ""}
                </Text>
                <TextInput
                  style={[styles.input, styles.disabledInput]}
                  value={value as string}
                  editable={false}
                  selectTextOnFocus={false}
                />
              </View>
            );
          })
        )}

        {/* Display uploaded files */}
        {formData.entityType && (
          <View style={styles.reviewSection}>
            {/* <Text style={styles.reviewSectionTitle}>Uploaded Documents</Text> */}
            {formData.entityType === "Limited Liability Company" && (
              <>
                {renderFileReview("form9", "Form 9 Business Registration form")}
                {renderFileReview("form24", "Form 24 Allotment of Shares")}
                {renderFileReview(
                  "form49",
                  "Form 49 Directors Particulars and Company Secretory Info"
                )}
                {renderFileReview("memArticle", "Memorandom of Articles M&A")}
                {renderFileReview("lan", "Latest Annual Returns")}
                {renderFileReview(
                  "bankStatement",
                  "Last 3 Month Bank Statements"
                )}
                {renderFileReview(
                  "icCard",
                  "Identity Cards of Directors-IC Card"
                )}
              </>
            )}
            {formData.entityType === "Sole Proprietor" && (
              <>
                {renderFileReview(
                  "formD",
                  "Form D from ROC(SSM) -Peninsular Dealer"
                )}
                {renderFileReview(
                  "form1",
                  "Form 1 and Biz registration certificate (Sabah Sarawak)"
                )}
                {renderFileReview(
                  "icCard",
                  "Identity Cards of Directors-IC Card"
                )}
              </>
            )}
          </View>
        )}
      </View>
    );
  };

  const renderFileReview = (field: string, label: string) => {
    const fileData = formData[field] as FileData | string;
    if (!fileData) return null;

    const handleFilePress = async (url: string) => {
      try {
        const supported = await Linking.canOpenURL(url);
        if (supported) {
          await Linking.openURL(url);
        } else {
          Alert.alert("Error", "Cannot open this file type");
        }
      } catch (error) {
        console.error("Error opening file:", error);
        Alert.alert("Error", "Failed to open file");
      }
    };

    // Check if fileData is a string (URL) or FileData object
    const displayText: any =
      typeof fileData === "string" ? fileData.split("/").pop() : fileData.name;
    const urlToOpen: any =
      typeof fileData === "string" ? fileData.split("/").pop() : fileData?.uri;

    return (
      <View key={field} style={styles.reviewField}>
        <Text style={styles.reviewLabel}>{label}</Text>
        <View style={styles.fileReviewContainer}>
          <View style={styles.fileIconContainer}>
            <Feather name="file" size={20} color="#1E60AE" />
          </View>
          <TouchableOpacity
            style={styles.fileNameContainer}
            onPress={() => handleFilePress(urlToOpen)}
          >
            <Text
              style={styles.fileName}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {displayText}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderInputField = (inputField: any, index: number) => {
    if (currentPosition === 2) {
      // Step 3
      return renderStep3Content();
    }

    if (currentPosition === 3) {
      // Step 4
      return renderStep4Content();
    }

    // Handle other steps
    const fieldName = inputField.fieldName || inputField.fieldLabel;
    const fieldValue = (formData[fieldName] as string) ?? "";

    return (
      <View key={index} style={styles.inputContainer}>
        <Text style={styles.inputLabel}>
          {inputField.label}
          {inputField.isMandatory ? "*" : ""}
        </Text>

        {inputField.isInputDropdown ? (
          <>
            <Picker
              selectedValue={fieldValue || "Select type of your company"}
              onValueChange={(value) => handleInputChange(fieldName, value)}
              style={{ borderWidth: 1, borderColor: "red" }}
              dropdownIconColor="white"
            >
              {inputField.options?.map((option: any, idx: number) => (
                <Picker.Item
                  key={idx}
                  label={option.label}
                  value={option.value}
                />
              ))}
            </Picker>
            <Image
              source={ChevronICon}
              style={[styles.icon, { width: 24, height: 24 }]}
            />
          </>
        ) : (
          <TextInput
            style={styles.input}
            value={fieldValue}
            onChangeText={(value) => handleInputChange(fieldName, value)}
            keyboardType={inputField.type === "NUMBER" ? "numeric" : "default"}
          />
        )}
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <View style={styles.container}>
        <View style={styles.headerContainer}>
          <HeaderStyle4 title={companyDetails.headerTitle} canGoBack={true} />
          <Text style={styles.pageDescription}>
            {companyDetails.pageDescription}
          </Text>
          <CustomStepIndicator
            currentPosition={currentPosition}
            stepCount={companyDetails.steps.length}
          />
        </View>

        <View style={styles.formContainer}>
          <View>
            <Text style={styles.stepLabel}>
              {companyDetails.steps[currentPosition]?.title}
            </Text>
          </View>
          <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
            {currentPosition === 2
              ? renderStep3Content()
              : currentPosition === 3
              ? renderStep4Content()
              : companyDetails.steps[currentPosition]?.input.map(
                  renderInputField
                )}
          </ScrollView>

          <View
            style={[
              styles.buttonContainer,
              currentPosition > 0 && {
                flexDirection: "row",
                gap: 10,
                justifyContent: "center",
              },
            ]}
          >
            {currentPosition > 0 && (
              <TouchableOpacity
                onPress={handleBack}
                style={styles.backButton}
                disabled={isLoading}
              >
                <Text style={styles.backLabel}>Back</Text>
              </TouchableOpacity>
            )}
            {currentPosition === companyDetails.steps.length - 1 ? (
              <TouchableOpacity
                onPress={() => navigation.navigate("ApplicationSubmitted")}
                style={[
                  styles.continueButton,
                  {
                    width: currentPosition > 0 ? "50%" : "100%",
                    alignSelf: "center",
                    borderWidth: 1,
                    borderColor: "#1E60AE",
                    backgroundColor: "#1E60AE",
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: 8,
                    height: 56,
                    opacity: isStepValid() ? 1 : 0.5,
                  },
                ]}
                disabled={isLoading || !isStepValid()}
              >
                {isLoading ? (
                  <ActivityIndicator color="#FFF" />
                ) : (
                  <Text style={styles.continueLabel}>Submit</Text>
                )}
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={handleNext}
                style={[
                  styles.continueButton,
                  {
                    width: currentPosition > 0 ? "50%" : "100%",
                    opacity: isStepValid() ? 1 : 0.5,
                  },
                ]}
                disabled={isLoading || !isStepValid()}
              >
                {isLoading ? (
                  <ActivityIndicator color="#FFF" />
                ) : (
                  <Text style={styles.continueLabel}>Continue</Text>
                )}
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const StepDot = ({ index, active, completed }: any) => {
  const backgroundColor = active
    ? "#1E60AE"
    : completed
    ? "#222222"
    : "#FFFFFF";

  const textColor = active || completed ? "#FFF" : "#000";
  const borderColor = active ? "#1E60AE" : completed ? "#222222" : "#D3D3D3";

  return (
    <View
      style={[
        styles.stepDot,
        {
          backgroundColor,
          borderColor,
        },
      ]}
    >
      <Text style={[styles.dotLabel, { color: textColor }]}>{index + 1}</Text>
    </View>
  );
};

const DottedLine = ({ completed }: any) => {
  return (
    <View style={styles.dottedLine}>
      {Array.from({ length: 3 }).map((_, index) => (
        <View
          key={index}
          style={[
            styles.dot,
            {
              backgroundColor: completed ? "black" : "gray",
              // marginHorizontal: 3,
            },
          ]}
        />
      ))}
    </View>
  );
};

const CustomStepIndicator = ({ currentPosition, stepCount }: any) => {
  return (
    <View style={styles.containerNew}>
      <View style={styles.row}>
        {Array.from({ length: stepCount }).map((_, index) => (
          <React.Fragment key={index}>
            <StepDot
              index={index}
              active={index === currentPosition}
              completed={index < currentPosition}
            />
            {index < stepCount - 1 && (
              <DottedLine completed={index < currentPosition} />
            )}
          </React.Fragment>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    padding: 20,
    gap: 10,
  },
  pageDescription: {
    ...FONTS.fontRegular,
    textAlign: "center",
    color: "#808080",
  },
  formContainer: {
    flex: 1,
    padding: 30,
    gap: 10,
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
    overflow: "hidden",
  },
  stepLabel: {
    ...FONTS.fontMedium,
    fontSize: 20,
  },
  inputContainer: {
    marginBottom: 15,
  },
  inputLabel: {
    ...FONTS.fontSm,
    marginBottom: 5,
    color: "#6C7278",
  },
  input: {
    borderWidth: 1,
    borderRadius: 10,
    borderColor: "lightgray",
    ...FONTS.fontSm,
    color: "#6C7278",
    padding: 10,
  },
  buttonContainer: {
    marginTop: 20,
  },
  continueButton: {
    backgroundColor: "#1E60AE",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    height: 56,
  },
  continueLabel: {
    ...FONTS.font,
    color: "#FFF",
    fontSize: 16,
    ...SORA.SoraRegular,
  },
  backButton: {
    backgroundColor: "#FFF",
    borderWidth: 1,
    borderColor: "#1E60AE",
    width: "50%",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    height: 56,
  },
  backLabel: {
    color: "#1E60AE",
    fontWeight: "600",
    fontSize: 16,
    ...SORA.SoraRegular,
  },
  containerNew: {
    paddingVertical: 16,
    alignItems: "center",
  },
  row: {
    flexDirection: "row",
    width: "50%",
    justifyContent: "space-around",
    alignItems: "center",
  },
  stepDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  dotLabel: {
    color: "#FFF",
    fontSize: 10,
  },
  dottedLine: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 5,
    gap: 1,
  },
  dot: {
    width: 3,
    height: 3,
    borderRadius: 34,
    marginHorizontal: 1,
  },
  pickerContainer: {
    position: "relative",
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 5,
    marginBottom: 10,
    overflow: "hidden",
  },
  picker: {
    height: 40,
    width: "100%",
    // backgroundColor:"red",
    borderWidth: 10,
    borderColor: "green",
  },
  icon: {
    position: "absolute",
    right: 10,
    top: "65%",
    transform: [{ translateY: -10 }],
    color: "#333",
    pointerEvents: "none",
  },
  fileUploadContainer: {
    marginBottom: 20,
  },
  uploadButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "gray",
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  uploadText: {
    marginLeft: 8,
    color: "#1E60AE",
    ...FONTS.font,
  },
  filePreview: {
    marginTop: 15,
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    // padding: 8,
    // borderWidth: 1,
  },
  filePreviewRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  fileInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    // flex: 1,
    gap: 8,
    width:"80%"
  },
  fileName: {
    flex: 1,
    ...FONTS.font,
    color: "#1E60AE",
  },
  reviewContainer: {
    padding: 16,
  },
  reviewSection: {
    // marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: "#EDF1F3",
    paddingTop: 16,
  },
  reviewSectionTitle: {
    ...FONTS.fontMedium,
    fontSize: 18,
    marginBottom: 16,
    color: "#1E60AE",
  },
  reviewField: {
    marginBottom: 16,
  },
  reviewLabel: {
    ...FONTS.fontSm,
    color: "#6C7278",
    marginBottom: 4,
  },
  reviewValue: {
    ...FONTS.font,
    color: "#222222",
    fontSize: 16,
  },
  fileReviewContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    padding: 12,
    borderRadius: 8,
    marginTop: 4,
  },
  fileIconContainer: {
    marginRight: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  fileNameContainer: {
    // flex: 1,
    width: "80%",
  },
  fileName: {
    ...FONTS.font,
    color: "#1E60AE",
    textDecorationLine: "underline",
  },
  disabledInput: {
    backgroundColor: "#F5F5F5",
    color: "#222222",
    borderColor: "lightgray",
  },
  stepTitle: {
    ...FONTS.fontMedium,
    fontSize: 18,
    color: "#1E60AE",
    marginTop: 20,
    marginBottom: 10,
  },
});
export default CompanyRegistration;
