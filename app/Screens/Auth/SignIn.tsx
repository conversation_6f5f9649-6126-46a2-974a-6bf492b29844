// import { useTheme } from "@react-navigation/native";
// import React, { useState, useEffect } from "react";
// import {
//   View,
//   Text,
//   Image,
//   TouchableOpacity,
//   SafeAreaView,
//   Alert,
//   Linking,
// } from "react-native";
// import { FONTS, COLORS } from "../../constants/theme";
// import { GlobalStyleSheet } from "../../constants/StyleSheet";
// import CustomInput from "../../components/Input/CustomInput";
// import Button from "../../components/Button/Button";
// import { Feather } from "@expo/vector-icons";
// import SocialBtn from "../../components/Socials/SocialBtn";
// import { FontAwesome } from "@expo/vector-icons";
// import { ScrollView } from "react-native-gesture-handler";
// import { IMAGES } from "../../constants/Images";
// import { StackScreenProps } from "@react-navigation/stack";
// import { RootStackParamList } from "../../Navigations/RootStackParamList";
// import { gql, useMutation } from "@apollo/client";
// import client from "../../api/appoloClient";
// import { authorize } from "react-native-app-auth";
// import { WebView, WebViewNavigation } from "react-native-webview";
// import {
//   useAuthRequest,
//   makeRedirectUri,
//   exchangeCodeAsync,
// } from "expo-auth-session";
// import * as Crypto from "expo-crypto";
// import * as SecureStore from "expo-secure-store";
// import { useShopifyAuth } from "../../api/shopifyAuth";
// import {
//   generateCodeVerifier,
//   generateCodeChallenge,
// } from "../../helpers/authHelpers";
// import "react-native-gesture-handler";
// type SignInScreenProps = StackScreenProps<RootStackParamList, "SignIn">;

// const SHOPIFY_STORE_URL = "https://sunrise-trade.myshopify.com";
// const SHOPIFY_CLIENT_ID = "shp_14fe4f79-cc74-42bb-8f31-e072f2ff10d4";
// const SHOPIFY_SHOP = "sunrise-trade";
// const CALLBACK_URL = "https://shop.***********.app://callback";

// const SHOPIFY_SHOP_ID = "***********";
// const REDIRECT_URI = makeRedirectUri({ scheme: "shop.***********.app" });
// const AUTH_URL = `https://shopify.com/authentication/${SHOPIFY_SHOP_ID}/oauth/authorize`;

// const discovery = {
//   authorizationEndpoint: `https://${SHOPIFY_SHOP}.myshopify.com/authentication/oauth/authorize`,
//   tokenEndpoint: `https://${SHOPIFY_SHOP}.myshopify.com/authentication/oauth/token`,
// };

// const shopifyConfig = {
//   // issuer: "https://shopify.com/authentication/***********/oauth",
//   issuer: SHOPIFY_STORE_URL,
//   clientId: SHOPIFY_CLIENT_ID,
//   redirectUrl: "shop.***********.app://callback",
//   scopes: ["openid", "profile", "email"],
//   serviceConfiguration: {
//     authorizationEndpoint:
//       "https://shopify.com/authentication/***********/oauth/authorize",
//     tokenEndpoint: "https://shopify.com/authentication/***********/oauth/token",
//   },
// };

// const SignIn = ({ navigation }: SignInScreenProps) => {
//   const theme = useTheme();
//   const { colors }: { colors: any } = theme;
//   const [otp, setOtp] = useState("");

//   const [otpSent, setOtpSent] = useState(false);
//   const [email, setEmail] = useState("");
//   const [isLoggingIn, setIsLoggingIn] = useState(false);
//   const [authRequest, setAuthRequest]: any = useState(null);
//   const [authCode, setAuthCode] = useState<string | null>(null);

//   const [accessToken, setAccessToken] = useState<string | null>(null);
//   const [userEmail, setUserEmail] = useState<string | null>(null);

//   const { requestOtp, verifyOtp, loading } = useShopifyAuth();
//   const handleRequestOtp = async () => {
//     if (!email) {
//       Alert.alert("Error", "Please enter your email.");
//       return;
//     }
//     await requestOtp(email);
//     setOtpSent(true);
//   };

//   const handleVerifyOtp = async () => {
//     if (!otp) {
//       Alert.alert("Error", "Please enter the OTP.");
//       return;
//     }
//     const token = await verifyOtp(email, otp);
//     if (token) {
//       // Save token and navigate to home
//       navigation.navigate("DrawerNavigation", { screen: "Home" });
//     }
//   };

//   const [request, response, promptAsync] = useAuthRequest(
//     {
//       clientId: SHOPIFY_CLIENT_ID,
//       redirectUri: REDIRECT_URI,
//       scopes: ["openid", "email", "customer-account-api:full"],
//       responseType: "code",
//     },
//     discovery
//   );

//   useEffect(() => {
//     console.log("---resp", response);
//     if (response?.type === "success") {
//       console.log("--response", response);
//       const { code } = response.params;
//       exchangeForToken(code);
//     }
//   }, [response]);

//   async function exchangeForToken(code: string) {
//     console.log("--getting token");
//     try {
//       const tokenResponse = await exchangeCodeAsync(
//         {
//           clientId: SHOPIFY_CLIENT_ID,
//           code,
//           redirectUri: REDIRECT_URI,
//           extraParams: {
//             code_verifier: request?.codeVerifier || "",
//           },
//         },
//         discovery
//       );
//       console.log("---tokenRE", tokenResponse);
//       setAccessToken(tokenResponse.accessToken);
//       console.log("Access Token:", tokenResponse.accessToken);

//       fetchUserInfo(tokenResponse.accessToken);
//     } catch (error) {
//       console.error("Token Exchange Failed:", error);
//     }
//   }

//   // 🔹 Fetch user information (optional)
//   async function fetchUserInfo(token: string) {
//     try {
//       const response = await fetch(
//         `https://${SHOPIFY_SHOP}.myshopify.com/customer/account`,
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             "Content-Type": "application/json",
//           },
//         }
//       );

//       const data = await response.json();
//       setUserEmail(data?.email || "Unknown");
//       console.log("User Info:", data);
//     } catch (error) {
//       console.error("Failed to Fetch User Info:", error);
//     }
//   }
//   return (
//     <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
//       <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
//         <View style={[GlobalStyleSheet.container, { padding: 0 }]}>
//           <View>
//             <Image
//               style={{
//                 height: null,
//                 aspectRatio: 2.3 / 1,
//                 width: "100%",
//                 borderBottomRightRadius: 100,
//               }}
//               source={IMAGES.item4}
//             />
//           </View>
//         </View>
//         <View
//           style={[
//             GlobalStyleSheet.container,
//             { paddingHorizontal: 30, paddingTop: 30 },
//           ]}
//         >
//           <View>
//             <Text
//               style={{
//                 ...FONTS.fontMedium,
//                 fontSize: 24,
//                 color: colors.title,
//                 marginBottom: 5,
//               }}
//             >
//               Sign in to your account
//             </Text>
//             <Text
//               style={{
//                 ...FONTS.fontRegular,
//                 fontSize: 15,
//                 color: colors.title,
//               }}
//             >
//               Welcome Back You've Been Missed!
//             </Text>
//           </View>
//           <View style={{ marginBottom: 15, marginTop: 30 }}>
//             <Text
//               style={{
//                 ...FONTS.fontRegular,
//                 fontSize: 15,
//                 color: colors.title,
//               }}
//             >
//               Email Address<Text style={{ color: "#FF0000" }}>*</Text>
//             </Text>
//             <CustomInput
//               onChangeText={setEmail}
//               // onChangeText={(value: any) => console.log(value)}
//             />
//           </View>
//           <View style={{ marginTop: 20 }}>
//             {/* <Button title="Request OTP" onPress={handleRequestOtp} /> */}
//             <Button title="Login with Shopify" onPress={() => promptAsync()} />
//           </View>

//           {/* <Button
//             title="Sign in with Shopify"
//             btnRounded
//             fullWidth
//             onPress={() => promptAsync()} // Call Shopify login
//             // disabled={!request} // Disable if request isn't ready
//             color={colors.title}
//           /> */}
//           <View style={{ marginTop: 72 }}>
//             <Button
//               title={"Sign in"}
//               btnRounded
//               fullWidth
//               icon={
//                 <Feather size={24} color={colors.title} name={"arrow-right"} />
//               }
//               // onPress={handleSendOtp}

//               onPress={() => navigation.navigate("CompanyRegistration")}
//               color={colors.title}
//             />
//             <View
//               style={{
//                 flexDirection: "row",
//                 alignItems: "center",
//                 marginTop: 37,
//                 marginBottom: 30,
//               }}
//             >
//               <View
//                 style={{
//                   height: 1,
//                   flex: 1,
//                   backgroundColor: colors.title,
//                 }}
//               />
//               <Text
//                 style={{
//                   ...FONTS.fontMedium,
//                   color: colors.text,
//                   marginHorizontal: 15,
//                   fontSize: 13,
//                 }}
//               >
//                 Or continue with
//               </Text>
//               <View
//                 style={{
//                   height: 1,
//                   flex: 1,
//                   backgroundColor: colors.title,
//                 }}
//               />
//             </View>
//             <View style={{ marginBottom: 10 }}>
//               <SocialBtn
//                 icon={
//                   <Image
//                     style={{ height: 20, width: 20, resizeMode: "contain" }}
//                     source={IMAGES.google2}
//                   />
//                 }
//                 rounded
//                 color={"#E8E2DB"}
//                 text={"Sign in with google"}
//               />
//             </View>
//             <View>
//               <SocialBtn
//                 icon={
//                   <FontAwesome name="apple" size={20} color={COLORS.title} />
//                 }
//                 rounded
//                 color={"#E8E2DB"}
//                 text={"Sign in with apple"}
//               />
//             </View>
//             <View
//               style={{
//                 alignItems: "center",
//                 marginTop: 20,
//                 flexDirection: "row",
//                 justifyContent: "center",
//               }}
//             >
//               <Text
//                 style={{
//                   ...FONTS.fontRegular,
//                   fontSize: 15,
//                   color: colors.title,
//                 }}
//               >
//                 Not a member?
//               </Text>
//               <TouchableOpacity onPress={() => navigation.navigate("SignUp")}>
//                 <Text
//                   style={{
//                     ...FONTS.fontMedium,
//                     borderBottomWidth: 1,
//                     borderBottomColor: colors.title,
//                     color: colors.title,
//                   }}
//                 >
//                   {" "}
//                   Create an account
//                 </Text>
//               </TouchableOpacity>
//             </View>
//           </View>
//         </View>
//       </SafeAreaView>
//     </ScrollView>
//   );
// };

// export default SignIn;

import React, { useEffect, useState } from "react";
import {
  View,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  Linking,
} from "react-native";
import { WebView, WebViewNavigation } from "react-native-webview";
import { useNavigation } from "@react-navigation/native";
import { exchangeCodeAsync } from "expo-auth-session";
import Button from "../../components/Button/Button";
import { FONTS } from "../../constants/theme";
import * as SecureStore from "expo-secure-store";
//<EMAIL>
const SHOPIFY_SHOP = "sunrise-trade";
const CLIENT_ID = "shp_14fe4f79-cc74-42bb-8f31-e072f2ff10d4";
const REDIRECT_URI = "shop.***********.app://auth/callback";

const AUTH_URL = `https://${SHOPIFY_SHOP}.myshopify.com/auth/oauth/authorize?client_id=${CLIENT_ID}&scope=openid,email,customer-account-api:full&redirect_uri=${encodeURIComponent(REDIRECT_URI)}&response_type=code`;

// Handle deep linking
const handleDeepLink = ({ url }: { url: string }) => {
  console.log('Received deep link:', url);
  if (url && url.startsWith(REDIRECT_URI)) {
    const urlObj = new URL(url);
    const code = urlObj.searchParams.get('code'); 
    if (code) {
      handleAuthCode(code);
    }
  }
};

// Set up deep link handlers
useEffect(() => {
  // Handle deep link when app is already running
  const subscription = Linking.addEventListener('url', handleDeepLink);

  // Handle deep link when app is not running and launched via URL
  Linking.getInitialURL().then(url => {
    if (url) {
      handleDeepLink({ url });
    }
  });

  return () => {
    subscription.remove();
  };
}, []);

async function handleAuthCode(code: string) {
  try {
    const tokenResponse = await exchangeCodeAsync(
      {
        clientId: CLIENT_ID,
        code,
        redirectUri: REDIRECT_URI,
        extraParams: {
          grant_type: "authorization_code",
        },
      },
      discovery
    );
    
    if (tokenResponse?.accessToken) {
      await SecureStore.setItemAsync('shopify_token', tokenResponse.accessToken);
      navigation.navigate("DrawerNavigation", { screen: "Home" });
    }
  } catch (error) {
    console.error('Error exchanging code for token:', error);
    Alert.alert('Error', 'Failed to complete authentication');
  }
}
const TOKEN_URL = `https://${SHOPIFY_SHOP}.myshopify.com/auth/oauth/token`;
const USER_INFO_URL = `https://${SHOPIFY_SHOP}.myshopify.com/customer/account`;

// Configure Shopify OAuth endpoints
const discovery = {
  authorizationEndpoint: `https://${SHOPIFY_SHOP}.myshopify.com/auth/oauth/authorize`,
  tokenEndpoint: TOKEN_URL,
  userInfoEndpoint: USER_INFO_URL,
};
//<EMAIL>
const SignIn = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [webViewVisible, setWebViewVisible] = useState(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [userInfo, setUserInfo] = useState<any>(null);

  const handleNavigationStateChange = async (event: WebViewNavigation) => {
    if (event.url.startsWith(REDIRECT_URI)) {
      setWebViewVisible(false);
      const url = new URL(event.url);
      const authCode = url.searchParams.get("code");
      const error = url.searchParams.get("error");

      if (error) {
        Alert.alert("Error", "Failed to authenticate with Shopify");
        return;
      }

      if (authCode) {
        await handleAuthCode(authCode);
      }
    }
  };

  async function exchangeForToken(code: string) {
    console.log('Starting token exchange with code:', code);
    setLoading(true);
    try {
      console.log('Token exchange params:', {
        clientId: CLIENT_ID,
        redirectUri: REDIRECT_URI,
        code: code
      });
      
      const tokenResponse = await exchangeCodeAsync(
        {
          clientId: CLIENT_ID,
          code,
          redirectUri: REDIRECT_URI,
          extraParams: {
            grant_type: "authorization_code",
          },
        },
        discovery
      );

      console.log('Token response received:', tokenResponse);
      const token = tokenResponse.accessToken;
      console.log('Access token:', token);
      
      await SecureStore.setItemAsync('shopify_token', token);
      setAccessToken(token);
      
      // Fetch user info after getting token
      await fetchUserInfo(token);
      
      Alert.alert(
        "Success", 
        "Login Successful!",
        [{ text: "OK", onPress: () => navigation.navigate("DrawerNavigation", { screen: "Home" }) }]
      );
    } catch (error) {
      console.error("Token Exchange Failed:", error);
      Alert.alert("Login Failed", "Please try again.");
    } finally {
      setLoading(false);
    }
  }

  async function fetchUserInfo(token: string) {
    console.log('Fetching user info with token:', token);
    try {
      const response = await fetch(USER_INFO_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      console.log('User info response status:', response.status);
      if (!response.ok) {
        console.error('User info response not OK:', response.status, response.statusText);
        throw new Error('Failed to fetch user info');
      }

      const data = await response.json();
      console.log('User info data:', data);
      await SecureStore.setItemAsync('user_info', JSON.stringify(data));
      setUserInfo(data);
    } catch (error) {
      console.error("Failed to fetch user info:", error);
      Alert.alert("Warning", "Could not fetch user information");
    }
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView contentContainerStyle={{ flexGrow: 1, padding: 20 }}>
        <View style={{ alignItems: "center", marginTop: 50 }}>
          <Text style={{ ...FONTS.fontMedium, fontSize: 24 }}>
            Sign in to your account
          </Text>
        </View>

        {!webViewVisible ? (
          <View style={{ marginTop: 40 }}>
            <Button title="Login with Shopify" onPress={() => setWebViewVisible(true)} />
          </View>
        ) : (
          <View style={{ flex: 1, height: 600 }}>
            <WebView
              source={{ uri: AUTH_URL }}
              onNavigationStateChange={handleNavigationStateChange}
              onLoadStart={() => setLoading(true)}
              onLoadEnd={() => setLoading(false)}
            />
            {loading && <ActivityIndicator size="large" style={{ marginTop: 20 }} />}
          </View>
        )}

        {accessToken && (
          <View style={{ marginTop: 20 }}>
            <Text style={{ textAlign: "center" }}>Access Token: {accessToken}</Text>
          </View>
        )}

        <TouchableOpacity
          onPress={() => navigation.navigate("SignUp")}
          style={{ marginTop: 30, alignSelf: "center" }}
        >
          <Text style={{ ...FONTS.fontMedium, borderBottomWidth: 1 }}>
            Create an account
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SignIn;
