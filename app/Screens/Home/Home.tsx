import React, {
  useRef,
  useState,
  useEffect,
  useMemo,
  useCallback,
  memo,
} from "react";
import {
  View,
  Image,
  Text,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  TextInput,
  Animated,
  StyleSheet,
  TouchableWithoutFeedback,
  SafeAreaView,
} from "react-native";
import { gql, useQuery } from "@apollo/client";

import {
  GET_HOMEPAGE_BANNERS,
  GET_COLLECTION_PRODUCTS,
  GET_HOMEPAGE_CMS,
  HOME_PAGE_FEATURED_COLLECTION,
} from "../../api/homepageQuery";
import { ApolloProvider } from "@apollo/client";
import client from "../../api/appoloClient";

import { GlobalStyleSheet } from "../../constants/StyleSheet";
import Swiper from "react-native-swiper/src";
import { IMAGES } from "../../constants/Images";
import Header from "../../layout/Header";

import {
  But<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>LOR<PERSON>,
  FON<PERSON>,
  FON<PERSON><PERSON><PERSON><PERSON><PERSON>,
  SIZ<PERSON>,
} from "../../constants/theme";
import { Colors } from "react-native/Libraries/NewAppScreen";

import { GestureHandlerRootView } from "react-native-gesture-handler";
import SearchImg from "../../assets/icons/search.png";
import { useNavigation } from "@react-navigation/native";
import { ActivityIndicator } from "react-native-paper";
import { RootStackParamList } from "../../Navigations/RootStackParamList";
import { StackScreenProps } from "@react-navigation/stack";
import Button from "../../components/Button/Button";
import CardHeader from "../../components/CardHeader/CardHeader";
import CardSlider from "../../components/CardSlider/CardSlider";
import AddToCartCard from "../../components/AddToCartCard/AddToCartCard";
import SingleBannerImage, {
  SingleSmallBannerImage,
} from "../../components/SingleBannerImage/SingleBannerImage";
import RedirectIcon from "../../assets/icons/redirectionindicationicon.png";
import SingleCollectionBannerImage from "../../components/SingleBannerImage/SingleCollectionBannerImage";
import AutoScrollBanner from "../../components/AutoScrollBanner/AutoScrollBanner";
import "react-native-gesture-handler";

const Home = ({ navigation }: any) => {
  // const navigation = useNavigation();

  const [opacity] = useState(new Animated.Value(1));
  const [selectedCategory3, setSelectedCategory3] = useState<string | null>(
    null
  );
  const [selectedCategory5, setSelectedCategory5] = useState<string | null>(
    null
  );
  const {
    loading,
    error,
    data: homePageData,
  } = useQuery(HOME_PAGE_FEATURED_COLLECTION);

  useEffect(() => {
    const collectionNodes = homePageData?.Product_by_coll_3?.nodes || [];

    if (collectionNodes.length > 0 && !selectedCategory3) {
      const collectionField = collectionNodes[0].fields.find(
        (f: any) => f.key === "choose_collection"
      );
      const defaultCategory = collectionField?.reference?.title || "Unknown";
      setSelectedCategory3(defaultCategory);
    }
  }, [homePageData, selectedCategory3]);
  useEffect(() => {
    if (
      selectedCategory5 === null &&
      homePageData?.Product_by_coll_5?.nodes?.length
    ) {
      const firstCategory =
        homePageData?.Product_by_coll_5.nodes[0].fields.find(
          (f: any) => f.key === "choose_collection"
        )?.reference?.title || null;

      setSelectedCategory5(firstCategory);
    }
  }, [homePageData, selectedCategory5]);

  if (error)
    return (
      <View>
        <Text> Error: {error.message} </Text>
      </View>
    );

  const sectionNameToQueryKey: any = {
    "top Banner slider": "top Banner slider",
    "Explore our category": "Explore our category",
    "Single banner 1": "Single banner 1",
    "product by collection 1": "product by collection 1",
    "Single-banner-2": "Single-banner-2",
    "product by collection 2": "product by collection 2",
    "Single-banner-3": "Single-banner-3",
    "APP - Shop by Row 2": "APP - Shop by Row 2",
    "product by collection 3": "product by collection 3",
    "Single-banner-4": "Single-banner-4",
    "product by collection 4": "product by collection 4",
    "product by collection 5": "product by collection 5",
    "Single-banner-5": "Single-banner-5 ",
    "APP - Shop by Row (3)": "APP - Shop by Row (3)",
    "Basketball Collection": "Basketball Collection",
    "Football Collection": "Football Collection",
    "Single-banner-6": "Single-banner-6",
  };

  const normalizeSectionNodes = (nodes: any) => {
    return nodes?.map((node: any) => {
      const section: any = {};
      node?.fields.forEach((field: any) => {
        section[field.key] = field.value;
      });
      return section;
    });
  };

  const rawSections = homePageData?.Section_sorting.nodes;

  const sections = normalizeSectionNodes(rawSections);

  const visibleSortedSections = sections
    ?.filter((section: any) => section.show_section === "true")
    .sort((a: any, b: any) => Number(a.position) - Number(b.position));

  const handleFocus = () => {
    navigation.navigate("Search");
  };

  const renderSectionComponent = (
    sectionName: string,
    section: any,
    index: number
  ) => {
    switch (sectionName) {
      case "top Banner slider":
        let sectionData = [];
        sectionData = homePageData?.App_top_banner_slider?.nodes || [];
        const banners = sectionData?.map((item: any) => {
          const fields: any = {};
          item?.fields?.forEach((field: any) => {
            fields[field.key] = field;
          });

          return {
            imageUrl: fields?.add_image_banner?.reference?.image?.url,
            handle: fields.select_collection_url?.reference?.handle,
          };
        });
        return (
          <View style={[GlobalStyleSheet.container, { padding: 0 }]}>
            <View style={{ zIndex: 12, marginVertical: 10 }}>
              <AutoScrollBanner banners={banners} navigation={navigation} />
            </View>
          </View>
        );
      case "Explore our category":
        const exploreCategoryData = homePageData?.Explore_category?.nodes || [];
        const sectionTitle = exploreCategoryData?.[0]?.fields?.find(
          (f: any) => f.key === "section_title"
        )?.value;

        // Extract all category items
        const categories = exploreCategoryData?.map((node: any) => {
          const fields: any = {};
          node?.fields.forEach((f: any) => {
            fields[f.key] = f;
          });

          return {
            imageUrl: fields.image?.reference?.image?.url,
            title: fields.title?.value,
            handle: fields.select_collection_?.reference?.handle,
          };
        });

        return (
          <View style={{ marginTop: 40 }}>
            <View style={{ marginHorizontal: 16 }}>
              <CardHeader
                cardHeaderTitle={sectionTitle}
                viewAllProducts={{ title: "View All", data: "data" }}
              />
            </View>
            <View style={{ marginTop: 15, marginLeft: 17 }}>
              <CardSlider products={categories} />
            </View>
          </View>
        );
      case "Single banner 1":
        const singleBannerNode = homePageData?.Single_banner?.nodes?.[0];

        const imageUrl = singleBannerNode?.fields?.find(
          (f: any) => f.key === "add_banner"
        )?.reference?.image?.url;

        const handle = singleBannerNode?.fields?.find(
          (f: any) => f.key === "select_collection_for_redirection"
        )?.reference?.handle;
        return (
          <View style={{ paddingHorizontal: 16, marginTop: 40 }}>
            <SingleBannerImage
              badge={{ badgeTitle: "Pre Order", bg: "white" }}
              imgURL={imageUrl}
              handle={handle}
            />
          </View>
        );
      case "product by collection 1":
        const productNode =
          homePageData?.Product_by_coll_1?.nodes?.[0]?.fields?.find(
            (f: any) => f.key === "choose_collection"
          );

        const products =
          productNode?.reference?.products?.edges?.map((edge: any) => {
            const product = edge.node;

            return {
              title: product.title,
              handle: product.handle,
              image: product.featuredImage?.src,
              vendor: product.vendor,
              variants: product.variants?.edges?.map((variantEdge: any) => {
                const variant = variantEdge.node;

                return {
                  title: variant.title,
                  price: variant?.price?.amount || "0.00", // Make sure price is being queried
                  image: variant?.image?.src || product.featuredImage?.src, // Fallback
                };
              }),
            };
          }) || [];

        return (
          <View
            style={{
              marginLeft: 16,
              marginTop: 10,
              backgroundColor: COLORS.background,
            }}
          >
            <AddToCartCard products={products} />
          </View>
        );
      case "Single-banner-2": {
        const singleBannerNode = homePageData?.Single_banner_2?.nodes?.[0];

        const imageUrl = singleBannerNode?.fields?.find(
          (f: any) => f.key === "select_image_banner"
        )?.reference?.image?.url;

        const handle = singleBannerNode?.fields?.find(
          (f: any) => f.key === "select_collection_for_redirection"
        )?.reference?.handle;

        return (
          <View style={{ paddingHorizontal: 16, marginTop: 40 }}>
            <SingleBannerImage
              // badge={{ badgeTitle: "New", bg: "white" }}
              imgURL={imageUrl}
              handle={handle}
            />
          </View>
        );
      }
      case "product by collection 2": {
        const collectionNode = homePageData?.Product_by_coll_2?.nodes?.[0];

        const fields = collectionNode?.fields || [];

        const sectionTitle = fields.find(
          (f: any) => f.key === "section_title"
        )?.value;

        const products =
          fields.find((f: any) => f.key === "choose_collection")?.reference
            ?.products?.edges || [];

        const formattedProducts = products?.map((productEdge: any) => {
          const product = productEdge.node;

          return {
            title: product.title,
            handle: product.handle,
            image: product.featuredImage?.src,
            vendor: product.vendor,
            variants: product.variants.edges?.map((variant: any) => ({
              title: variant.node?.title,
              price: variant.node?.price?.amount || "0.00",
              image: variant.node?.image?.src || product.featuredImage?.src,
            })),
          };
        });

        return (
          <View
            style={{
              // marginLeft: 16,
              marginTop: 40,
              backgroundColor: COLORS.background,
            }}
          >
            <View style={{ marginHorizontal: 16 }}>
              <CardHeader
                cardHeaderTitle={sectionTitle}
                viewAllProducts={{
                  title: "View All",
                  data: "data",
                }}
              />
            </View>
            <View style={{ marginTop: 15, marginLeft: 16 }}>
              <AddToCartCard products={formattedProducts} />
            </View>
          </View>
        );
      }
      case "Single-banner-3": {
        const singleBannerNode = homePageData?.Single_banner_3?.nodes?.[0];

        const imageUrl = singleBannerNode?.fields?.find(
          (f: any) => f.key === "select_image_banner"
        )?.reference?.image?.url;

        const handle = singleBannerNode?.fields?.find(
          (f: any) => f.key === "select_collection_for_redirection"
        )?.reference?.handle;
        return (
          <View style={{ marginTop: 40 }}>
            <SingleSmallBannerImage
              // badge={{ badgeTitle: "New", bg: "white" }}
              imgURL={imageUrl}
              handle={handle}
              fullImage={true}
            />
          </View>
        );
      }
      case "APP - Shop by Row 2": {
        const nodes = homePageData?.BadmintoCollection?.nodes || [];

        const formattedCategories = nodes?.map((node: any) => {
          const fields = node?.fields || [];

          const collectionField = fields.find(
            (f: any) => f.key === "collection_handle"
          );
          const imageField = fields.find((f: any) => f.key === "image");

          const handle = collectionField?.value || "";
          const title = handle;
          const imageUrl = imageField?.reference?.image?.url || "";

          return {
            title,
            handle,
            imageUrl,
          };
        });

        return (
          <View style={{ marginTop: 10 }}>
            <View style={{ marginTop: 10 }}>
              <CardSlider products={formattedCategories} smallCard={true} />
            </View>
          </View>
        );
      }
      case "product by collection 3": {
        const sectionKey = "product by collection 3";
        const collectionNodes = homePageData?.Product_by_coll_3?.nodes || [];

        const formattedCategoryData = collectionNodes?.map((node: any) => {
          const collectionField = node?.fields.find(
            (f: any) => f.key === "choose_collection"
          );
          const sectionTitleField = node?.fields.find(
            (f: any) => f.key === "section_title"
          );

          const categoryTitle = collectionField?.reference?.title || "Unknown";
          const sectionTitle = sectionTitleField?.value || categoryTitle;

          const products = collectionField?.reference?.products?.edges || [];

          const formattedProducts = products?.map((productEdge: any) => {
            const product = productEdge.node;

            return {
              title: product.title,
              handle: product.handle,
              image: product.featuredImage?.src,
              vendor: product.vendor,
              variants: product.variants.edges?.map((variant: any) => ({
                title: variant.node?.title,
                price: variant?.node?.price?.amount || "0.00",
                image: variant?.node?.image?.src || product.featuredImage?.src,
              })),
            };
          });

          return {
            categoryTitle,
            sectionTitle,
            products: formattedProducts,
          };
        });

        // Wait until category is set
        if (!selectedCategory3) {
          return (
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                height: 600,
              }}
            >
              <ActivityIndicator size="large" color={COLORS.primary} />
            </View>
          );
        }

        return (
          <View style={{ marginTop: 40, gap: 15 }}>
            <View style={{ marginHorizontal: 16 }}>
              <CardHeader
                cardHeaderTitle={
                  formattedCategoryData.find(
                    (cat: any) => cat.categoryTitle === selectedCategory3
                  )?.sectionTitle || "Shop"
                }
                viewAllProducts={{
                  title: "View All",
                  data: "data",
                }}
              />
            </View>

            {/* Category Tabs */}
            <View style={{ marginHorizontal: 5 }}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{
                  gap: 10,
                  flexDirection: "row",
                  paddingHorizontal: 10,
                }}
                style={{ width: "100%" }}
              >
                {formattedCategoryData?.map((cat: any, index: any) => (
                  <TouchableOpacity
                    key={cat.categoryTitle + index}
                    onPress={() => setSelectedCategory3(cat.categoryTitle)}
                    style={{
                      backgroundColor:
                        selectedCategory3 === cat.categoryTitle
                          ? "black"
                          : "white",
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: 20,
                      borderWidth: 1,
                      borderColor: COLORS.darkgray,
                      paddingVertical: 8,
                      paddingHorizontal: 20,
                    }}
                  >
                    <Text
                      style={{
                        color:
                          selectedCategory3 === cat.categoryTitle
                            ? COLORS.white
                            : COLORS.darkgray,
                        fontSize: SIZES.fontLg,
                        fontFamily: "DMSansBlackItalic",
                      }}
                    >
                      {cat.categoryTitle}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Product List for Selected Category */}
            {formattedCategoryData
              ?.filter((cat: any) => cat.categoryTitle === selectedCategory3)
              ?.map((cat: any) => (
                <View
                  key={cat.categoryTitle + "_section"}
                  style={{
                    marginLeft: 16,
                    backgroundColor: COLORS.background,
                  }}
                >
                  <AddToCartCard products={cat.products} />
                </View>
              ))}
          </View>
        );
      }
      case "Single-banner-4": {
        const singleBannerNode = homePageData?.Single_banner_4?.nodes?.[0];

        const imageUrl = singleBannerNode?.fields?.find(
          (f: any) => f.key === "select_image_banner"
        )?.reference?.image?.url;

        const handle = singleBannerNode?.fields?.find(
          (f: any) => f.key === "select_collection_for_redirection"
        )?.reference?.handle;
        return (
          <View style={{ marginTop: 40 }}>
            <SingleSmallBannerImage
              // badge={{ badgeTitle: "New", bg: "white" }}
              imgURL={imageUrl}
              handle={handle}
              fullImage={true}
            />
          </View>
        );
      }
      case "product by collection 4": {
        const collectionNode = homePageData?.Product_by_coll_4?.nodes?.[0];

        const fields = collectionNode?.fields || [];

        const sectionTitle = fields.find(
          (f: any) => f.key === "section_title"
        )?.value;

        const products =
          fields.find((f: any) => f.key === "choose_collection")?.reference
            ?.products?.edges || [];

        const formattedProducts = products?.map((productEdge: any) => {
          const product = productEdge.node;

          return {
            title: product.title,
            handle: product.handle,
            image: product.featuredImage?.src,
            vendor: product.vendor,
            variants: product.variants.edges?.map((variant: any) => ({
              title: variant.node?.title,
              price: variant.node?.price?.amount || "0.00",
              image: variant.node?.image?.src || product.featuredImage?.src,
            })),
          };
        });

        return (
          <View
            style={{
              marginLeft: 16,
              marginTop: 10,
              backgroundColor: COLORS.background,
            }}
          >
            <AddToCartCard products={formattedProducts} />
          </View>
        );
      }
      case "product by collection 5": {
        const sectionKey = "product by collection 5";
        const collectionNodes = homePageData?.Product_by_coll_5?.nodes || [];

        const formattedCategoryData = collectionNodes?.map((node: any) => {
          const collectionField = node?.fields.find(
            (f: any) => f.key === "choose_collection"
          );
          const sectionTitleField = node?.fields.find(
            (f: any) => f.key === "section_title"
          );

          const categoryTitle = collectionField?.reference?.title || "Unknown";
          const sectionTitle = sectionTitleField?.value || categoryTitle;

          const products = collectionField?.reference?.products?.edges || [];

          const formattedProducts = products?.map((productEdge: any) => {
            const product = productEdge.node;

            return {
              title: product.title,
              handle: product.handle,
              image: product.featuredImage?.src,
              vendor: product.vendor,
              variants: product.variants.edges?.map((variant: any) => ({
                title: variant.node?.title,
                price: variant?.node?.price?.amount || "0.00",
                image: variant?.node?.image?.src || product.featuredImage?.src,
              })),
            };
          });

          return {
            categoryTitle,
            sectionTitle,
            products: formattedProducts,
          };
        });

        // Wait until category is set
        if (!selectedCategory5) {
          return (
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                height: 600,
              }}
            >
              <ActivityIndicator size="large" color={COLORS.primary} />
            </View>
          );
        }
        return (
          <View style={{ marginTop: 40, gap: 15 }}>
            {/* Section Header */}
            <View style={{ marginHorizontal: 16 }}>
              <CardHeader
                cardHeaderTitle={
                  formattedCategoryData.find(
                    (cat: any) => cat.categoryTitle === selectedCategory5
                  )?.sectionTitle || "Shop"
                }
                viewAllProducts={{
                  title: "View All",
                  data: "data",
                }}
              />
            </View>

            {/* Category Tabs */}
            <View style={{ marginHorizontal: 5 }}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{
                  gap: 10,
                  flexDirection: "row",
                  paddingHorizontal: 10,
                }}
                style={{ width: "100%" }}
              >
                {formattedCategoryData?.map((cat: any, index: any) => (
                  <TouchableOpacity
                    key={cat.categoryTitle + index}
                    onPress={() => setSelectedCategory5(cat.categoryTitle)}
                    style={{
                      backgroundColor:
                        selectedCategory5 === cat.categoryTitle
                          ? "black"
                          : "white",
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: 20,
                      borderWidth: 1,
                      borderColor: COLORS.darkgray,
                      paddingVertical: 8,
                      paddingHorizontal: 20,
                    }}
                  >
                    <Text
                      style={{
                        color:
                          selectedCategory5 === cat.categoryTitle
                            ? COLORS.white
                            : COLORS.darkgray,
                        fontSize: SIZES.fontLg,
                        fontFamily: "DMSansBlackItalic",
                      }}
                    >
                      {cat.categoryTitle}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Product List for Selected Category */}
            {formattedCategoryData
              ?.filter((cat: any) => cat.categoryTitle === selectedCategory5)
              ?.map((cat: any) => (
                <View
                  key={cat.categoryTitle + "_section"}
                  style={{
                    marginLeft: 16,
                    backgroundColor: COLORS.background,
                  }}
                >
                  <AddToCartCard products={cat.products} />
                </View>
              ))}
          </View>
        );
      }
      case "Single-banner-5": {
        const singleBannerNode = homePageData?.Single_banner_5?.nodes?.[0];

        const imageUrl = singleBannerNode?.fields?.find(
          (f: any) => f.key === "select_image_banner"
        )?.reference?.image?.url;

        const handle = singleBannerNode?.fields?.find(
          (f: any) => f.key === "select_collection_for_redirection"
        )?.reference?.handle;
        return (
          <View style={{ marginTop: 40 }}>
            <SingleSmallBannerImage
              // badge={{ badgeTitle: "New", bg: "white" }}
              imgURL={imageUrl}
              handle={handle}
              fullImage={true}
            />
          </View>
        );
      }
      case "APP - Shop by Row (3)": {
        const exploreCategoryData = homePageData?.APP_Shop_By_Row?.nodes || [];

        // Extract all category items
        const categories = exploreCategoryData?.map((node: any) => {
          const fields: any = {};
          node?.fields.forEach((f: any) => {
            fields[f.key] = f;
          });

          return {
            imageUrl: fields.image?.reference?.image?.url,
            title: fields.title?.value,
            handle: fields.select_collection_?.reference?.handle,
          };
        });

        return (
          <View
            style={[
              {
                flexDirection: "row",
                marginTop: 10,
                justifyContent: "center",
                marginBottom: 40,
                gap: 10,
              },
            ]}
          >
            {categories?.map((data: any, index: any) => {
              return (
                <View
                  style={[
                    {
                      width: 192,
                      height: 183,
                      backgroundColor: COLORS.background,
                      borderRadius: 15,
                    },
                  ]}
                  key={data.image + data.title}
                >
                  <TouchableOpacity
                    activeOpacity={0.7}
                    style={{
                      borderWidth: 1,
                      borderColor: "white",
                      borderRadius: 15,
                      backgroundColor: COLORS.card,
                    }}
                    onPress={() => {
                      navigation.navigate("ProductListingPage", {
                        handle: data?.handle,
                      });
                    }}
                  >
                    <Image
                      style={{
                        width: "100%",
                        height: 138,
                        objectFit: "cover",
                        // aspectRatio: 2 / 2,
                        borderTopLeftRadius: 15,
                        borderTopRightRadius: 15,
                      }}
                      source={{
                        uri:
                          data?.imageUrl ??
                          "https://media.istockphoto.com/id/1761333789/photo/badminton-shuttlecocks-and-racket-placed-in-the-corner-of-a-synthetic-field.jpg?s=612x612&w=0&k=20&c=3rr4BZqe1rDWsCe6LF_YPCXZe6Um5jizc6d6n96U1Q4=",
                      }}
                    />
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-evenly",
                        alignItems: "center",
                        marginVertical: 10,
                        paddingHorizontal: 16,
                        width: "100%",
                        // borderWidth:1
                      }}
                    >
                      <Text
                        style={{
                          ...FONTS.fontSemiBold,
                          color: COLORS.black,
                          fontSize: SIZES.font,
                          width: "75%",
                          flexWrap: "wrap",
                        }}
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {data?.title}
                      </Text>
                      <Image
                        source={RedirectIcon}
                        style={{
                          width: 25,
                          height: 25,
                          marginRight: 10,
                        }}
                      />
                    </View>
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        );
      }
      case "Basketball Collection": {
        const collectionNodes =
          homePageData?.Basketball_Collection?.nodes || [];

        const formattedCategoryData = collectionNodes?.map((node: any) => {
          const collectionField = node?.fields.find(
            (f: any) => f.key === "choose_collection"
          );
          const sectionTitleField = node?.fields.find(
            (f: any) => f.key === "section_title"
          );
          const imageField = node?.fields.find((f: any) => f.key === "image");

          const categoryTitle = collectionField?.reference?.title || "Unknown";
          const sectionTitle = sectionTitleField?.value || categoryTitle;
          const imageUrl =
            imageField?.reference?.image?.url ||
            "https://via.placeholder.com/600x200?text=No+Image";

          const products = collectionField?.reference?.products?.edges || [];

          const formattedProducts = products?.map((productEdge: any) => {
            const product = productEdge.node;

            return {
              title: product.title,
              handle: product.handle,
              image: product.featuredImage?.src,
              vendor: product.vendor,
              variants: product.variants.edges?.map((variant: any) => ({
                title: variant.node?.title,
                price: variant?.node?.price?.amount || "0.00",
                image: variant?.node?.image?.src || product.featuredImage?.src,
              })),
            };
          });

          return {
            categoryTitle,
            sectionTitle,
            products: formattedProducts,
            bannerImage: imageUrl,
          };
        });

        return (
          <View style={{}}>
            {formattedCategoryData?.map((cat: any) => (
              <View key={cat.categoryTitle + "_section"}>
                {/* Section Header */}
                <View style={{ marginHorizontal: 16 }}>
                  <CardHeader
                    cardHeaderTitle={cat.sectionTitle}
                    viewAllProducts={{
                      title: "View All",
                      data: "data",
                    }}
                  />
                </View>

                {/* Product List with Banner Image */}
                {/* <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                  scrollEventThrottle={16}
                  style={{ backgroundColor: COLORS.background }}
                > */}
                <View
                  style={{
                    marginTop: 15,
                    marginLeft: 16,
                    backgroundColor: COLORS.background,
                    gap: 10,
                    flexDirection: "row",
                  }}
                >
                  {/* <AddToCartCard products={cat.products}> */}
                  {/* <View style={{ marginTop: 5 }}> */}
                  <SingleCollectionBannerImage imgUrl={cat.bannerImage} />
                  <AddToCartCard products={cat.products} />
                </View>
                {/* </ScrollView> */}
              </View>
            ))}
          </View>
        );
      }
      case "Football Collection": {
        const collectionNodes = homePageData?.Football_Collection?.nodes || [];

        const formattedCategoryData = collectionNodes?.map((node: any) => {
          const collectionField = node?.fields.find(
            (f: any) => f.key === "choose_collection"
          );
          const sectionTitleField = node?.fields.find(
            (f: any) => f.key === "section_title"
          );
          const imageField = node?.fields.find((f: any) => f.key === "image");

          const categoryTitle = collectionField?.reference?.title || "Unknown";
          const sectionTitle = sectionTitleField?.value || categoryTitle;
          const imageUrl =
            imageField?.reference?.image?.url ||
            "https://via.placeholder.com/600x200?text=No+Image";

          const products = collectionField?.reference?.products?.edges || [];

          const formattedProducts = products?.map((productEdge: any) => {
            const product = productEdge.node;

            return {
              title: product.title,
              handle: product.handle,
              image: product.featuredImage?.src,
              vendor: product.vendor,
              variants: product.variants.edges?.map((variant: any) => ({
                title: variant.node?.title,
                price: variant?.node?.price?.amount || "0.00",
                image: variant?.node?.image?.src || product.featuredImage?.src,
              })),
            };
          });

          return {
            categoryTitle,
            sectionTitle,
            products: formattedProducts,
            bannerImage: imageUrl,
          };
        });

        return (
          <View style={{ marginTop: 40 }}>
            {formattedCategoryData?.map((cat: any) => (
              <View key={cat.categoryTitle + "_section"}>
                {/* Section Header */}
                <View style={{ marginHorizontal: 16 }}>
                  <CardHeader
                    cardHeaderTitle={cat.sectionTitle}
                    viewAllProducts={{
                      title: "View All",
                      data: "data",
                    }}
                  />
                </View>

                {/* Product List with Banner Image */}
                {/* <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                  scrollEventThrottle={16}
                  style={{ backgroundColor: COLORS.background }}
                  
                > */}
                <View
                  style={{
                    marginTop: 15,
                    marginLeft: 16,
                    backgroundColor: COLORS.background,
                    gap: 10,
                    flexDirection: "row",
                  }}
                >
                  {/* <AddToCartCard products={cat.products}> */}
                  {/* <View style={{ marginTop: 5 }}> */}
                  <SingleCollectionBannerImage imgUrl={cat.bannerImage} />
                  <AddToCartCard products={cat.products} />
                </View>
                {/* </ScrollView> */}
              </View>
            ))}
          </View>
        );
      }
      case "Single-banner-6": {
        const singleBannerNode = homePageData?.Single_banner_6?.nodes?.[0];

        const getFieldValue = (key: string) =>
          singleBannerNode?.fields?.find((f: any) => f.key === key)?.value;

        const getFieldReferenceHandle = (key: string) =>
          singleBannerNode?.fields?.find((f: any) => f.key === key)?.reference
            ?.handle;

        const getImageUrl = (key: string) =>
          singleBannerNode?.fields?.find((f: any) => f.key === key)?.reference
            ?.image?.url;

        const imageUrl = getImageUrl("select_image_banner");
        const handle = getFieldReferenceHandle("button_link");
        const title = getFieldValue("title");
        const description = getFieldValue("description");
        const buttonTitle = getFieldValue("button_title");

        return (
          <View style={{ marginTop: 40 }}>
            <SingleSmallBannerImage imgURL={imageUrl} fullImage={true} />

            <View
              style={[
                GlobalStyleSheet.container,
                {
                  justifyContent: "center",
                  alignItems: "center",
                  gap: 10,
                },
              ]}
            >
              {title && (
                <Text
                  style={{
                    ...FONTS.fontMedium,
                    ...FONTWEIGHT.SemiBold,
                    ...FONTS.h4,
                  }}
                >
                  {title}
                </Text>
              )}

              {description && (
                <Text
                  style={{
                    textAlign: "center",
                    ...FONTWEIGHT.SemiBold,
                    ...FONTS.font,
                    ...FONTS.fontTitle,
                    lineHeight: 22,
                  }}
                >
                  {description}
                </Text>
              )}

              {handle && (
                <TouchableOpacity
                  onPress={() => {
                    // TODO: Navigate to collection by handle
                    navigation.navigate("ProductListingPage", { handle });
                  }}
                  style={{
                    backgroundColor: COLORS.black,
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 25,
                  }}
                >
                  <Text style={{ color: COLORS.white, ...FONTS.fontMedium }}>
                    {buttonTitle}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        );
      }

      default:
        return null;
    }
  };

  return (
    <SafeAreaView
      style={{ backgroundColor: COLORS.background, flex: 1, marginBottom: 0 }}
    >
      <Header title={""} leftIcon={"sunriselogo"} titleLeft paddingLeft />

      <ScrollView showsVerticalScrollIndicator={false}>
        <GestureHandlerRootView style={styles.container}>
          <ScrollView>
            {loading ? (
              <View
                style={{
                  justifyContent: "center",
                  alignItems: "center",
                  height: 600,
                }}
              >
                <ActivityIndicator size="large" color={COLORS.primary} />
              </View>
            ) : (
              <View style={{}}>
                <View
                  style={{ marginTop: 20, backgroundColor: Colors.background }}
                >
                  <TouchableOpacity
                    activeOpacity={1}
                    onPress={handleFocus}
                    style={{
                      height: 48,
                      flex: 1,
                      borderRadius: 30,
                    }}
                  >
                    <Image
                      source={SearchImg}
                      style={{
                        position: "absolute",
                        width: 18,
                        height: 18,
                        top: 12,
                        left: 25,
                        zIndex: 1000,
                        shadowColor: "red",
                        shadowOffset: { x: 0, y: 0 },
                        shadowOpacity: 0.5,
                        shadowRadius: 25,
                      }}
                    />
                    <TextInput
                      style={{
                        ...FONTS.fontRegular,
                        fontSize: 16,
                        color: "black",
                        paddingLeft: 40,
                        flex: 1,
                        marginHorizontal: 10,
                        borderRadius: 30,
                        backgroundColor: "white",
                      }}
                      placeholder="Search"
                      placeholderTextColor="gray"
                      editable={false}
                    />
                  </TouchableOpacity>

                  <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ backgroundColor: COLORS.background }}
                  >
                    {visibleSortedSections?.map((section: any, index: any) => {
                      const sectionName: any = section.section_name;

                      // If section is not in our list, skip rendering
                      if (!sectionNameToQueryKey[sectionName]) return null;

                      // Match section data from GraphQL response
                      return renderSectionComponent(
                        sectionName,
                        section,
                        index
                      );
                    })}
                  </ScrollView>
                </View>
              </View>
            )}
          </ScrollView>
        </GestureHandlerRootView>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    backgroundColor: "red ",
    alignItems: "center",
  },
});

export default Home;
