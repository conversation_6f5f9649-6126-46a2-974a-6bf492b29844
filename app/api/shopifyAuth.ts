import * as AuthSession from 'expo-auth-session';
import * as Crypto from 'expo-crypto';
import * as SecureStore from 'expo-secure-store';
import { useEffect, useState } from 'react';
import { Alert } from 'react-native';

const SHOPIFY_STORE_URL = 'https://sunrise-trade.myshopify.com';
const CLIENT_ID = 'shp_14fe4f79-cc74-42bb-8f31-e072f2ff10d4';
const REDIRECT_URI = AuthSession.makeRedirectUri(); 
const CUSTOMER_ACCOUNT_API_URL = `${SHOPIFY_STORE_URL}/api/2023-10/graphql.json`;
async function generateCodeVerifier() {
    const randomBytes = await Crypto.getRandomBytesAsync(32);
    return Buffer.from(randomBytes).toString('base64url');
}

async function generateCodeChallenge(codeVerifier: string) {
    const hashed = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        codeVerifier
    );
    return Buffer.from(hashed, 'hex').toString('base64url');
}

// export function useShopifyAuth() {
//     const [request, response, promptAsync] = AuthSession.useAuthRequest(
//         {
//             clientId: CLIENT_ID,
//             responseType: 'code',
//             redirectUri: REDIRECT_URI,
//             scopes: ['openid', 'email', 'customer-account-api:full'],
//             extraParams: {
//                 code_challenge: '', 
//                 code_challenge_method: 'S256',
//                 state: 'random_state_1234',
//                 nonce: 'random_nonce_5678',
//             },
//         },
//         { authorizationEndpoint: `${SHOPIFY_STORE_URL}/authentication/***********/oauth/authorize` }
//     );

//     useEffect(() => {
//         async function handleAuthResponse() {
//             if (response?.type === 'success' && response.params.code) {
//                 console.log('Auth Code:', response.params.code);
//                 await exchangeCodeForToken(response.params.code);
//             }
//         }
//         handleAuthResponse();
//     }, [response]);

//     return { request, promptAsync };
// }

// Exchange Authorization Code for Access Token
async function exchangeCodeForToken(code: string) {
    const codeVerifier = await SecureStore.getItemAsync('code_verifier');

    if (!codeVerifier) {
        throw new Error('Code verifier not found');
    }

    const body = new URLSearchParams();
    body.append('grant_type', 'authorization_code');
    body.append('client_id', CLIENT_ID);
    body.append('redirect_uri', REDIRECT_URI);
    body.append('code', code);
    body.append('code_verifier', codeVerifier);

    const headers = { 'Content-Type': 'application/x-www-form-urlencoded' };

    try {
        const response = await fetch(`${SHOPIFY_STORE_URL}/authentication/***********/oauth/token`, {
            method: 'POST',
            headers,
            body,
        });

        const data = await response.json();
        console.log('Access Token:', data.access_token);

        await SecureStore.setItemAsync('access_token', data.access_token);
        return data.access_token;
    } catch (error) {
        console.error('Token Exchange Error:', error);
    }
}

export const useShopifyAuth = () => {
    const [loading, setLoading] = useState(false);
  
    // Request OTP
    const requestOtp = async (email: string) => {
      setLoading(true);
      try {
        const response = await fetch(CUSTOMER_ACCOUNT_API_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Shopify-Storefront-Access-Token": "a07ab8516e503385c9783460cd1abf3f",
          },
          body: JSON.stringify({
            query: `
              mutation customerGenerateAccountActivationToken($email: String!) {
                customerGenerateAccountActivationToken(email: $email) {
                  userErrors {
                    field
                    message
                  }
                }
              }
            `,
            variables: { email },
          }),
        });
  
        const result = await response.json();
        console.log("--res", result)
        if (result.data?.customerGenerateAccountActivationToken?.userErrors?.length) {
          Alert.alert("Error", result.data.customerGenerateAccountActivationToken.userErrors[0].message);
        } else {
          Alert.alert("Success", "OTP sent to your email.");
        }
      } catch (error) {
        Alert.alert("Error", "Failed to request OTP.");
        console.error(error);
      } finally {
        setLoading(false);
      }
    };
  
    // Verify OTP
    const verifyOtp = async (email: string, otp: string) => {
      setLoading(true);
      try {
        const response = await fetch(CUSTOMER_ACCOUNT_API_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Shopify-Storefront-Access-Token": "your_storefront_access_token",
          },
          body: JSON.stringify({
            query: `
              mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
                customerAccessTokenCreate(input: $input) {
                  customerAccessToken {
                    accessToken
                    expiresAt
                  }
                  userErrors {
                    field
                    message
                  }
                }
              }
            `,
            variables: {
              input: {
                email,
                otp,  // Pass the OTP here
              },
            },
          }),
        });
  
        const result = await response.json();
        const token = result.data?.customerAccessTokenCreate?.customerAccessToken?.accessToken;
  
        if (token) {
          Alert.alert("Success", "Logged in successfully!");
          return token;
        } else {
          Alert.alert("Error", "Invalid OTP.");
        }
      } catch (error) {
        Alert.alert("Error", "Failed to verify OTP.");
        console.error(error);
      } finally {
        setLoading(false);
      }
    };
  
    return { requestOtp, verifyOtp, loading };
  };
  
