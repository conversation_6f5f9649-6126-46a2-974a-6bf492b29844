{"expo": {"name": "Sunrise", "slug": "Sunrise", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "scheme": "shop.65050247268.app", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.stc.sunrise"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.stc.sunrise", "buildType": "apk", "enableProguardInReleaseBuilds": false, "enableHermes": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "4cfa0b95-980f-452b-816f-64a8ed8e3061"}}}}